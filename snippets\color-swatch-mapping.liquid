{%- comment -%}
  Color Swatch Mapping
  
  This snippet provides a centralized place to map your product color names
  to actual CSS colors. Customize this file to match your product colors.
  
  Usage: Include this in any template that needs color swatches
{%- endcomment -%}

<style>
  /* 
    Color Swatch Mapping - Customize these to match your actual product colors
    Add or modify colors based on your product variants
  */
  
  /* Primary Colors */
  .swatch--black { background-color: #000000; }
  .swatch--white { background-color: #ffffff; border: 2px solid #e0e0e0; }
  .swatch--red { background-color: #dc2626; }
  .swatch--blue { background-color: #2563eb; }
  .swatch--green { background-color: #16a34a; }
  .swatch--yellow { background-color: #facc15; }
  .swatch--purple { background-color: #9333ea; }
  .swatch--orange { background-color: #ea580c; }
  .swatch--pink { background-color: #f8bbd9; }
  .swatch--brown { background-color: #92400e; }
  
  /* Neutral Colors */
  .swatch--gray,
  .swatch--grey { background-color: #6b7280; }
  .swatch--beige { background-color: #f5f5dc; border: 1px solid #d0d0d0; }
  .swatch--cream { background-color: #fffdd0; border: 1px solid #d0d0d0; }
  .swatch--ivory { background-color: #fffff0; border: 1px solid #d0d0d0; }
  .swatch--khaki { background-color: #f0e68c; }
  .swatch--tan { background-color: #d2b48c; }
  
  /* Specific Color Variations */
  .swatch--navy,
  .swatch--navy-blue { background-color: #1e3a8a; }
  .swatch--royal-blue { background-color: #4169e1; }
  .swatch--sky-blue { background-color: #87ceeb; }
  .swatch--forest-green,
  .swatch--olive,
  .swatch--olive-green { background-color: #4a5d3a; }
  .swatch--lime-green { background-color: #32cd32; }
  .swatch--light-pink,
  .swatch--rose { background-color: #f8bbd9; }
  .swatch--hot-pink { background-color: #ff1493; }
  .swatch--burgundy,
  .swatch--wine { background-color: #800020; }
  .swatch--maroon { background-color: #800000; }
  .swatch--teal { background-color: #008080; }
  .swatch--turquoise { background-color: #40e0d0; }
  .swatch--coral { background-color: #ff7f50; }
  .swatch--salmon { background-color: #fa8072; }
  .swatch--gold { background-color: #ffd700; }
  .swatch--silver { background-color: #c0c0c0; }
  .swatch--bronze { background-color: #cd7f32; }
  
  /* Dark Colors */
  .swatch--charcoal { background-color: #36454f; }
  .swatch--slate { background-color: #708090; }
  .swatch--midnight { background-color: #191970; }
  
  /* 
    Add your specific product colors here:
    Example for "Ave City Jacket" colors:
  */
  .swatch--forest { background-color: #355e3b; }
  .swatch--dusty-pink { background-color: #dcae96; }
  
  /* Fallback for unmapped colors - shows a pattern */
  .swatch:not([class*="swatch--"]) {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 6px 6px;
    background-position: 0 0, 0 3px, 3px -3px, -3px 0px;
    border: 2px solid #ccc;
  }
</style>
