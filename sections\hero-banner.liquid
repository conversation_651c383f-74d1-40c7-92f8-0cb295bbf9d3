{%- comment -%}
  Hero Banner Section

  Marketplace-ready hero with image/video modes, full-bleed layout, and optional
  fullscreen behavior that can respect a sticky header height.
  Added custom/locked height support so the visible area matches selected height.
{%- endcomment -%}

{%- liquid
  assign base_vh = ''
  case section.settings.banner_height
    when 'small'      
      assign base_vh = 45
    when 'medium'
      assign base_vh = 60
    when 'large'
      assign base_vh = 80
    when 'custom'
      assign base_vh = section.settings.custom_height_vh
    when 'fullscreen'
      assign base_vh = 100
  endcase
  assign base_height_value = ''
  if section.settings.banner_height == 'fullscreen'
    if section.settings.subtract_fixed_header
      assign base_height_value = 'calc(100vh - var(--header-height, 100px))'
    else
      assign base_height_value = '100vh'
    endif
  elsif section.settings.banner_height == 'adapt'
    assign base_height_value = ''
  elsif base_vh != ''
    if section.settings.subtract_fixed_header
      assign base_height_value = 'calc(' | append: base_vh | append: 'vh - var(--header-height, 100px))'
    else
      assign base_height_value = base_vh | append: 'vh'
    endif
  endif
-%}

<section class="hero-banner hero--height-{{ section.settings.banner_height }}{% if section.settings.subtract_fixed_header %} hero--subtract-header{% endif %} color-{{ section.settings.color_scheme }}" 
         id="Hero-{{ section.id }}"
         {{ section.shopify_attributes }}
         {% if section.settings.banner_height == 'custom' %}data-custom-vh="{{ section.settings.custom_height_vh }}"{% endif %}
         {% if section.settings.banner_height == 'custom' or section.settings.lock_height %}
           style="{% if section.settings.banner_height == 'custom' %}--hero-custom-height: {{ section.settings.custom_height_vh }}vh;{% endif %}{% if section.settings.lock_height and base_height_value != '' %}height: {{ base_height_value }};{% endif %}"
         {% endif %}>
  {%- if section.settings.is_primary_hero == true -%}
    {%- assign load_attr = 'eager' -%}
    {%- assign pri_attr = 'high' -%}
  {%- else -%}
    {%- assign load_attr = 'lazy' -%}
    {%- assign pri_attr = 'auto' -%}
  {%- endif -%}

  {%- assign final_alt = section.settings.image_alt | default: section.settings.image.alt | default: section.settings.heading | escape -%}
  
  {%- comment -%} Background Media Layer {%- endcomment -%}
  <div class="hero__media" aria-hidden="true">
    {%- if section.settings.media_mode == 'video' and section.settings.video != blank -%}
      {%- liquid
        assign autoplay_video = section.settings.autoplay_video
        if section.settings.respect_reduced_motion
          assign autoplay_video = section.settings.autoplay_video
        endif
      -%}
      {{ section.settings.video | video_tag:
         autoplay: autoplay_video,
         muted: true,
         loop: true,
         playsinline: true,
         controls: section.settings.show_controls,
         poster: section.settings.video_poster,
         class: 'hero__video',
         data-autoplay: section.settings.autoplay_video }}
    {%- elsif section.settings.image != blank -%}
      <picture class="hero__picture">
        {%- if section.settings.use_mobile_image and section.settings.image_mobile != blank -%}
          <source media="(max-width: 749px)"
                  srcset="{{ section.settings.image_mobile | image_url: width: 750 }} 750w,
                          {{ section.settings.image_mobile | image_url: width: 1100 }} 1100w"
                  sizes="100vw">
        {%- endif -%}
        <source media="(min-width: 750px)"
                srcset="{{ section.settings.image | image_url: width: 750 }} 750w,
                        {{ section.settings.image | image_url: width: 1100 }} 1100w,
                        {{ section.settings.image | image_url: width: 1500 }} 1500w,
                        {{ section.settings.image | image_url: width: 2000 }} 2000w"
                sizes="100vw">
        {{ section.settings.image | image_url: width: 2000 | image_tag:
          loading: load_attr,
          fetchpriority: pri_attr,
          alt: final_alt,
          class: 'hero__image' }}
      </picture>
    {%- else -%}
      {%- comment -%} Fallback placeholder when no media is selected {%- endcomment -%}
      <div class="hero__placeholder">
        {{ 'hero-apparel-1' | placeholder_svg_tag: 'hero__placeholder-svg' }}
      </div>
    {%- endif -%}
  </div>

  {%- comment -%} Overlay Layer {%- endcomment -%}
  <div class="hero__overlay"
       {% unless section.settings.show_overlay %}hidden{% endunless %}
       data-style="{{ section.settings.overlay_style }}"
       style="--overlay-opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};"></div>

  {%- comment -%} Content Layer {%- endcomment -%}
  <div class="hero__content hero__content--pos-{{ section.settings.content_position }}"
       style="--content-max-width: {{ section.settings.content_max_width_ch }}ch; text-align: {{ section.settings.content_alignment }};">
    
    {%- if section.settings.heading != blank -%}
      <{{ section.settings.heading_tag }} class="hero__heading">
        {{ section.settings.heading }}
      </{{ section.settings.heading_tag }}>
    {%- endif -%}
    
    {%- if section.settings.text != blank -%}
      <div class="hero__text">
        {{ section.settings.text }}
      </div>
    {%- endif -%}
    
    {%- if section.settings.button_label != blank or section.settings.button2_label != blank -%}
      <div class="hero__buttons" data-layout="{{ section.settings.button_layout }}">
        {%- if section.settings.button_label != blank and section.settings.button_link != blank -%}
          <a class="button button--primary" href="{{ section.settings.button_link }}">
            {{ section.settings.button_label | escape }}
          </a>
        {%- endif -%}
        {%- if section.settings.button2_label != blank and section.settings.button2_link != blank -%}
          <a class="button button--secondary" href="{{ section.settings.button2_link }}">
            {{ section.settings.button2_label | escape }}
          </a>
        {%- endif -%}
      </div>
    {%- endif -%}
    
  </div>
</section>

{%- comment -%} Reduced Motion Support {%- endcomment -%}
{%- if section.settings.respect_reduced_motion -%}
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const prefersReduced = matchMedia('(prefers-reduced-motion: reduce)').matches;
      if (!prefersReduced) return;
      
      const video = document.querySelector('#Hero-{{ section.id }} .hero__video[data-autoplay="true"]');
      if (video) {
        video.pause();
        video.removeAttribute('autoplay');
      }
    });
  </script>
{%- endif -%}

{% stylesheet %}
  /* Hero Banner Base Styles */
  .hero-banner {
   position: relative;
   display: grid;
   isolation: isolate;
   /* Make the hero full-bleed to the viewport while keeping content constrained
     to the theme's page width using existing CSS variables. This mirrors the
     announcement/full-bleed pattern used elsewhere and avoids hard-coded values. */
   width: 100vw;
   margin-left: calc(-50vw + 50%);
  }

  .hero__media,
  .hero__overlay,
  .hero__content {
    grid-area: 1 / 1;
  }

  /* Media Styles */
  .hero__media {
   z-index: 1;
   /* Clip any intrinsic overflow from tall images so they don't expand the section
     beyond the configured min-height. Also ensure the media area takes the
     container height so children with object-fit behave reliably. */
   overflow: hidden;
   height: 100%;
  }

  .hero__picture,
  .hero__image,
  .hero__video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
   /* Prevent the browser from using an intrinsic image max-width that can
     interact oddly with our full-bleed container. */
   max-width: none;
  }

  .hero__placeholder {
    width: 100%;
    height: 100%;
    background-color: var(--color-background-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .hero__placeholder-svg {
    width: 120px;
    height: 120px;
    opacity: 0.3;
  }

  /* Height Variations - using height instead of min-height for reliable sizing */
  .hero-banner.hero--height-small { height: 45vh; }
  .hero-banner.hero--height-medium { height: 60vh; }
  .hero-banner.hero--height-large { height: 80vh; }
  .hero-banner.hero--height-fullscreen { height: 100vh; }
  .hero-banner.hero--height-adapt { height: auto; }
  .hero-banner.hero--height-custom { height: var(--hero-custom-height, 60vh); }
  
  /* Height variations when accounting for fixed header (uses dynamic --header-height provided by header JS) */
  .hero-banner.hero--subtract-header.hero--height-small { 
    height: calc(45vh - env(safe-area-inset-top, 0px) - var(--header-height, 100px));
  }
  .hero-banner.hero--subtract-header.hero--height-medium { 
    height: calc(60vh - env(safe-area-inset-top, 0px) - var(--header-height, 100px));
  }
  .hero-banner.hero--subtract-header.hero--height-large { 
    height: calc(80vh - env(safe-area-inset-top, 0px) - var(--header-height, 100px));
  }
  .hero-banner.hero--subtract-header.hero--height-fullscreen { 
    height: calc(100vh - env(safe-area-inset-top, 0px) - var(--header-height, 100px));
  }
  .hero-banner.hero--subtract-header.hero--height-custom { 
    height: calc(var(--hero-custom-height, 60vh) - env(safe-area-inset-top, 0px) - var(--header-height, 100px));
  }

  /* Ensure the media layer fills the container's height */
  .hero__media { min-height: inherit; }

  @media screen and (max-width: 749px) {
    .hero-banner.hero--height-small { height: 35vh; }
    .hero-banner.hero--height-medium { height: 50vh; }
    .hero-banner.hero--height-large { height: 70vh; }
    .hero-banner.hero--height-fullscreen { height: 100vh; }
    .hero-banner.hero--height-custom { height: var(--hero-custom-height, 60vh); }
    
    /* Mobile heights when accounting for fixed elements */
    .hero-banner.hero--subtract-header.hero--height-small { 
      height: calc(35vh - env(safe-area-inset-top, 0px) - var(--header-height, 80px));
    }
    .hero-banner.hero--subtract-header.hero--height-medium { 
      height: calc(50vh - env(safe-area-inset-top, 0px) - var(--header-height, 80px));
    }
    .hero-banner.hero--subtract-header.hero--height-large { 
      height: calc(70vh - env(safe-area-inset-top, 0px) - var(--header-height, 80px));
    }
    .hero-banner.hero--subtract-header.hero--height-fullscreen { 
      height: calc(100vh - env(safe-area-inset-top, 0px) - var(--header-height, 80px));
    }
    .hero-banner.hero--subtract-header.hero--height-custom { 
      height: calc(var(--hero-custom-height, 60vh) - env(safe-area-inset-top, 0px) - var(--header-height, 80px));
    }
  }

  /* Overlay Styles */
  .hero__overlay {
    z-index: 2;
    pointer-events: none;
    opacity: var(--overlay-opacity, 0.3);
  }

  .hero__overlay:not([hidden])[data-style="solid"] {
    background-color: rgba(0, 0, 0, var(--overlay-opacity, 0.3));
  }

  .hero__overlay:not([hidden])[data-style="gradient"] {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, calc(var(--overlay-opacity, 0.3) + 0.05)),
      rgba(0, 0, 0, 0)
    );
  }

  /* Content Styles */
  .hero__content {
    z-index: 3;
    display: grid;
    align-content: center;
    justify-items: center;
   /* Constrain content to the theme's page width and use the page margin variable
     for horizontal padding so we don't hard-code gutter sizes. */
   max-width: var(--page-width);
   margin: 0 auto;
   padding: 2rem var(--page-margin);
    gap: 1.5rem;
  }

  .hero__content > * {
    max-width: var(--content-max-width, 40ch);
  }

  .hero__heading {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: bold;
    line-height: 1.1;
    margin: 0;
    color: rgb(var(--color-foreground));
  }

  .hero__text {
    font-size: 1.125rem;
    line-height: 1.5;
    color: rgba(var(--color-foreground), 0.9);
  }

  .hero__text p {
    margin: 0;
  }

  .hero__text p + p {
    margin-top: 1rem;
  }

  /* 9-Point Content Positioning */
  .hero__content--pos-top-left {
    align-content: start;
    justify-items: start;
  }

  .hero__content--pos-top-center {
    align-content: start;
    justify-items: center;
  }

  .hero__content--pos-top-right {
    align-content: start;
    justify-items: end;
  }

  .hero__content--pos-center-left {
    align-content: center;
    justify-items: start;
  }

  .hero__content--pos-center {
    align-content: center;
    justify-items: center;
  }

  .hero__content--pos-center-right {
    align-content: center;
    justify-items: end;
  }

  .hero__content--pos-bottom-left {
    align-content: end;
    justify-items: start;
  }

  .hero__content--pos-bottom-center {
    align-content: end;
    justify-items: center;
  }

  .hero__content--pos-bottom-right {
    align-content: end;
    justify-items: end;
  }

  /* Button Styles */
  .hero__buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    align-items: center;
  }

  .hero__buttons[data-layout="stack"] {
    flex-direction: column;
  }

  @media screen and (max-width: 749px) {
    .hero__buttons[data-layout="auto"] {
      flex-direction: column;
    }
  }

  .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    border-radius: 0.375rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 48px;
    border: 2px solid transparent;
  }

  .button--primary {
    background-color: rgb(var(--color-button));
    color: rgb(var(--color-button-text));
    border-color: rgb(var(--color-button));
  }

  .button--primary:hover {
    background-color: rgba(var(--color-button), 0.9);
    transform: translateY(-1px);
  }

  .button--secondary {
    background-color: transparent;
    color: rgb(var(--color-foreground));
    border-color: rgba(var(--color-foreground), 0.5);
  }

  .button--secondary:hover {
    background-color: rgba(var(--color-foreground), 0.1);
    border-color: rgb(var(--color-foreground));
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    .hero-banner * {
      transition: none !important;
      animation: none !important;
    }

    .button:hover {
      transform: none !important;
    }
  }

  /* Focus States */
  .button:focus-visible {
    outline: 2px solid rgb(var(--color-button));
    outline-offset: 2px;
  }

  /* Mobile Optimizations */
  @media screen and (max-width: 749px) {
    .hero__content {
      padding: 1.5rem;
      gap: 1rem;
    }

    .hero__heading {
      font-size: clamp(1.75rem, 6vw, 2.5rem);
    }

    .hero__text {
      font-size: 1rem;
    }

    .button {
      padding: 0.875rem 1.5rem;
      width: 100%;
    }

    .hero__buttons[data-layout="auto"] .button,
    .hero__buttons[data-layout="stack"] .button {
      width: 100%;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "🎬 Hero banner",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "📱 Media"
    },
    {
      "type": "select",
      "id": "media_mode",
      "label": "Media mode",
      "options": [
        { "value": "image", "label": "Image" },
        { "value": "video", "label": "Video" }
      ],
      "default": "image",
      "info": "Choose between image or video background"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Desktop image",
      "info": "Recommended: 2000x1200px or larger for best quality",
      "visible_if": "{{ section.settings.media_mode == 'image' }}"
    },
    {
      "type": "checkbox",
      "id": "use_mobile_image",
      "label": "Use a different mobile image",
      "default": false,
      "info": "Upload a separate image optimized for mobile screens",
      "visible_if": "{{ section.settings.media_mode == 'image' }}"
    },
    {
      "type": "image_picker",
      "id": "image_mobile",
      "label": "Mobile image",
      "info": "Recommended: 750x1000px for mobile optimization",
      "visible_if": "{{ section.settings.media_mode == 'image' and section.settings.use_mobile_image == true }}"
    },
    {
      "type": "text",
      "id": "image_alt",
      "label": "Alt text override",
      "info": "Defaults to the image's alt text or heading if left empty",
      "visible_if": "{{ section.settings.media_mode == 'image' }}"
    },
    {
      "type": "video",
      "id": "video",
      "label": "Background video",
      "info": "Use Shopify-hosted video for best performance",
      "visible_if": "{{ section.settings.media_mode == 'video' }}"
    },
    {
      "type": "image_picker",
      "id": "video_poster",
      "label": "Video poster image",
      "info": "Shown while video loads and for users who prefer reduced motion",
      "visible_if": "{{ section.settings.media_mode == 'video' }}"
    },
    {
      "type": "checkbox",
      "id": "autoplay_video",
      "label": "Autoplay video",
      "default": true,
      "info": "Video will be muted for autoplay compliance",
      "visible_if": "{{ section.settings.media_mode == 'video' }}"
    },
    {
      "type": "checkbox",
      "id": "show_controls",
      "label": "Show video controls",
      "default": false,
      "info": "Allow users to control video playback",
      "visible_if": "{{ section.settings.media_mode == 'video' and section.settings.autoplay_video == false }}"
    },
    {
      "type": "checkbox",
      "id": "respect_reduced_motion",
      "label": "Respect reduced motion preference",
      "default": true,
      "info": "Pause autoplay for users who prefer reduced motion"
    },
    {
      "type": "header",
      "content": "✏️ Content"
    },
    {
      "type": "inline_richtext",
      "id": "heading",
      "label": "Heading",
      "default": "Hero banner heading",
      "info": "Use bold and italic formatting as needed"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading tag",
      "options": [
        { "value": "h1", "label": "H1" },
        { "value": "h2", "label": "H2" },
        { "value": "p", "label": "Paragraph" }
      ],
      "default": "h2",
      "info": "Choose H1 for homepage hero, H2 for other pages"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Subheading",
      "default": "<p>Add descriptive text to support your hero banner heading and create engaging content for your customers.</p>",
      "info": "Supports paragraphs, links, and basic formatting"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Primary button label",
      "default": "Shop now",
      "placeholder": "Shop now, Learn more, Get started..."
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Primary button link",
      "info": "Link to collection, product, or page"
    },
    {
      "type": "text",
      "id": "button2_label",
      "label": "Secondary button label",
      "placeholder": "Learn more, Contact us..."
    },
    {
      "type": "url",
      "id": "button2_link",
      "label": "Secondary button link",
      "info": "Optional second call-to-action"
    },
    {
      "type": "header",
      "content": "🎨 Layout and style"
    },
    {
      "type": "select",
      "id": "banner_height",
      "label": "Banner height",
      "options": [
        { "value": "small", "label": "Small (45vh)" },
        { "value": "medium", "label": "Medium (60vh)" },
        { "value": "large", "label": "Large (80vh)" },
        { "value": "fullscreen", "label": "Fullscreen (100vh)" },
        { "value": "adapt", "label": "Adapt to image height" },
        { "value": "custom", "label": "Custom" }
      ],
      "default": "large",
      "info": "Height is reduced on mobile for better usability"
    },
    {
      "type": "range",
      "id": "custom_height_vh",
      "label": "Custom height",
      "min": 20,
      "max": 100,
      "step": 5,
      "default": 60,
      "unit": "vh",
      "info": "Viewport height when Custom is selected",
      "visible_if": "{{ section.settings.banner_height == 'custom' }}"
    },
    {
      "type": "checkbox",
      "id": "lock_height",
      "label": "Lock exact height (crop overflow)",
      "default": false,
      "info": "Uses a fixed height instead of min-height so the visible area always matches the selected value.",
      "visible_if": "{{ section.settings.banner_height != 'adapt' }}"
    },
    {
      "type": "checkbox",
      "id": "subtract_fixed_header",
      "label": "Account for header and announcement bar",
      "default": false,
      "info": "When enabled, height represents visible area after subtracting header and announcement heights"
    },
    {
      "type": "select",
      "id": "content_position",
      "label": "Content position",
      "options": [
        { "value": "top-left", "label": "Top left" },
        { "value": "top-center", "label": "Top center" },
        { "value": "top-right", "label": "Top right" },
        { "value": "center-left", "label": "Center left" },
        { "value": "center", "label": "Center" },
        { "value": "center-right", "label": "Center right" },
        { "value": "bottom-left", "label": "Bottom left" },
        { "value": "bottom-center", "label": "Bottom center" },
        { "value": "bottom-right", "label": "Bottom right" }
      ],
      "default": "center",
      "info": "Position of text and buttons within the banner"
    },
    {
      "type": "radio",
      "id": "content_alignment",
      "label": "Text alignment",
      "options": [
        { "value": "left", "label": "Left" },
        { "value": "center", "label": "Center" },
        { "value": "right", "label": "Right" }
      ],
      "default": "left",
      "info": "Text alignment within content area"
    },
    {
      "type": "range",
      "id": "content_max_width_ch",
      "label": "Content max width",
      "min": 28,
      "max": 80,
      "step": 2,
      "default": 40,
      "unit": "ch",
      "info": "Maximum width of content area in characters"
    },
    {
      "type": "select",
      "id": "button_layout",
      "label": "Button layout",
      "options": [
        { "value": "auto", "label": "Auto (responsive)" },
        { "value": "row", "label": "Row (side by side)" },
        { "value": "stack", "label": "Stack (vertical)" }
      ],
      "default": "auto",
      "info": "Auto stacks buttons on mobile, row on desktop"
    },
    {
      "type": "checkbox",
      "id": "show_overlay",
      "label": "Show overlay",
      "default": true,
      "info": "Adds dark overlay to improve text readability"
    },
    {
      "type": "select",
      "id": "overlay_style",
      "label": "Overlay style",
      "options": [
        { "value": "solid", "label": "Solid" },
        { "value": "gradient", "label": "Gradient" }
      ],
      "default": "solid",
      "info": "Gradient fades from top to bottom",
      "visible_if": "{{ section.settings.show_overlay == true }}"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "Overlay opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 30,
      "unit": "%",
      "info": "Higher values create more contrast for text",
      "visible_if": "{{ section.settings.show_overlay == true }}"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1",
      "info": "Choose colors for text and buttons"
    },
    {
      "type": "checkbox",
      "id": "is_primary_hero",
      "label": "Treat as homepage hero for performance",
      "default": true,
      "info": "Enables eager loading and high priority for faster loading on homepage"
    }
  ],
  "presets": [
    {
      "name": "Hero banner",
      "settings": {
        "heading": "Hero banner heading",
        "text": "<p>Add descriptive text to support your hero banner heading and create engaging content for your customers.</p>",
        "button_label": "Shop now",
        "banner_height": "large",
        "content_position": "center",
        "is_primary_hero": true
      }
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
