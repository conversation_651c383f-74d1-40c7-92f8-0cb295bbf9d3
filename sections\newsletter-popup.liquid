{% comment %}
  Newsletter Popup Section
  - Global section, include once in layout/theme.liquid
  - Editor-driven with visible_if [条件可见]
  - CSP-safe (no inline JS), Web Component handles behavior
{% endcomment %}

{{ 'newsletter-popup.css' | asset_url | stylesheet_tag }}
<script type="module" defer src="{{ 'newsletter-popup.js' | asset_url }}"></script>

{% if section.settings.enable %}
<newsletter-popup
  class="nlp color-{{ section.settings.color_scheme }}"
  data-trigger="{{ section.settings.trigger }}"
  data-delay-seconds="{{ section.settings.delay_seconds }}"
  data-scroll-percent="{{ section.settings.scroll_percent }}"
  data-show-again-days="{{ section.settings.show_again_days }}"
  data-desktop-only="{{ section.settings.desktop_only }}"
  data-start-after-interaction="{{ section.settings.start_after_interaction }}"
  data-test-mode="{{ section.settings.test_mode }}"
  data-consent-gate="{{ section.settings.consent_gate }}"
  data-cookie-name="{{ section.settings.cookie_name | default: 'newsletterPopupClosed' }}"
  data-overlay-opacity="{{ section.settings.overlay_opacity | default: 40 }}"
  data-suppress-on-signup="{{ section.settings.suppress_on_signup }}"
  aria-label="{{ section.settings.aria_label | escape }}"
>
  <div class="nlp__backdrop" data-backdrop aria-hidden="true"></div>

  <div class="nlp__dialog nlp__dialog--{{ section.settings.layout }}" role="dialog" aria-modal="true">
    <button class="nlp__close" type="button" data-close aria-label="{{ 'general.close' | t | default: 'Close' }}">×</button>
      
      {% if section.settings.layout != 'no_image' and section.settings.image != blank %}
        <div class="nlp__media nlp__media--{{ section.settings.layout }}">
          {{ section.settings.image | image_url: width: 720 | image_tag: 
            loading: 'lazy', 
            widths: '360,540,720', 
            sizes: '(max-width: 749px) 90vw, 420px', 
            alt: section.settings.image.alt | default: '' 
          }}
        </div>
      {% endif %}
      
      <div class="nlp__content">
        {% if section.settings.heading != blank %}
          <h2 data-title class="nlp__heading">{{ section.settings.heading }}</h2>
        {% endif %}
        
        {% if section.settings.subheading != blank %}
          <div class="nlp__sub" id="nlp-description">{{ section.settings.subheading }}</div>
        {% endif %}
        
        {% form 'customer', id: 'NewsletterPopupForm', class: 'nlp__form' %}
          <input type="hidden" name="contact[tags]" value="newsletter">
          <label class="visually-hidden" for="NewsletterPopupEmail">Email</label>
          <input id="NewsletterPopupEmail" 
                 name="contact[email]" 
                 type="email" 
                 required
                 placeholder="{{ section.settings.placeholder | escape }}" 
                 autocomplete="email"
                 autofocus>
          <button type="submit" class="button button--primary">
            {{ section.settings.button_label }}
          </button>
        {% endform %}
        
        <div class="nlp__success" role="status" aria-live="polite" hidden>
          {{ section.settings.success_message }}
        </div>
      </div>
    </div>
  </newsletter-popup>
{% endif %}

{% schema %}
{
  "name": "Newsletter popup",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Behavior & Triggers"
    },
    {
      "type": "checkbox",
      "id": "enable",
      "label": "Enable popup",
      "default": false
    },
    {
      "type": "select",
      "id": "trigger",
      "label": "Trigger type",
      "options": [
        {
          "value": "time_delay",
          "label": "Time delay"
        },
        {
          "value": "scroll_depth",
          "label": "Scroll depth"
        },
        {
          "value": "exit_intent",
          "label": "Exit intent"
        }
      ],
      "default": "time_delay"
    },
    {
      "type": "range",
      "id": "delay_seconds",
      "min": 0,
      "max": 60,
      "step": 1,
      "unit": "s",
      "label": "Delay seconds",
      "default": 5,
      "visible_if": "{{ section.settings.trigger == 'time_delay' }}"
    },
    {
      "type": "range",
      "id": "scroll_percent",
      "min": 10,
      "max": 90,
      "step": 5,
      "unit": "%",
      "label": "Scroll percentage",
      "default": 50,
      "visible_if": "{{ section.settings.trigger == 'scroll_depth' }}"
    },
    {
      "type": "checkbox",
      "id": "desktop_only",
      "label": "Desktop only",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "start_after_interaction",
      "label": "Start timer after first user interaction",
      "default": true,
      "visible_if": "{{ section.settings.trigger == 'time_delay' }}"
    },
    {
      "type": "range",
      "id": "show_again_days",
      "min": 1,
      "max": 90,
      "step": 1,
      "unit": "d",
      "label": "Show again after (days)",
      "default": 30
    },
    {
      "type": "checkbox",
      "id": "test_mode",
      "label": "Test mode (always show)",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "consent_gate",
      "label": "Require marketing consent before showing",
      "default": false
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "text",
      "id": "cookie_name",
      "label": "Cookie name",
      "default": "newsletterPopupClosed"
    },
    {
      "type": "text",
      "id": "aria_label",
      "label": "Accessibility label",
      "default": "Newsletter signup"
    },
    {
      "type": "checkbox",
      "id": "suppress_on_signup",
      "label": "Suppress after successful signup",
      "default": true
    },
    {
      "type": "header",
      "content": "Content & Appearance"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "image_top",
          "label": "Image on top"
        },
        {
          "value": "image_left",
          "label": "Image left"
        },
        {
          "value": "image_right", 
          "label": "Image right"
        },
        {
          "value": "no_image",
          "label": "No image"
        }
      ],
      "default": "image_left"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "visible_if": "{{ section.settings.layout != 'no_image' }}"
    },
    {
      "type": "inline_richtext",
      "id": "heading",
      "label": "Heading",
      "default": "Hello"
    },
    {
      "type": "richtext",
      "id": "subheading",
      "label": "Subheading", 
      "default": "<p>Enter your email to see discount coupon</p>"
    },
    {
      "type": "text",
      "id": "placeholder",
      "label": "Email placeholder",
      "default": "Insert any email here"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button text",
      "default": "Get my 50% off"
    },
    {
      "type": "richtext",
      "id": "success_message",
      "label": "Success message",
      "default": "<p>Thank you for subscribing!</p>"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Overlay opacity",
      "default": 60
    }
  ],
  "presets": [
    {
      "name": "Newsletter popup",
      "settings": {
        "enable": true,
        "layout": "image_left",
        "heading": "Hello",
        "subheading": "<p>Enter your email to see discount coupon</p>",
        "placeholder": "Insert any email here",
        "button_label": "Get my 50% off",
        "success_message": "<p>Thank you for subscribing!</p>",
        "color_scheme": "scheme-1",
        "trigger": "timer",
        "delay_seconds": 3,
        "show_again_days": 7
      }
    }
  ]
}
{% endschema %}
