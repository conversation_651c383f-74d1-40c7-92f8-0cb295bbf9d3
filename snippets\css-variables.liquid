{% style %}
  {% # Loads all font variations with display: swap %}
  {{ settings.type_primary_font | font_face: font_display: 'swap' }}
  {{ settings.type_primary_font | font_modify: 'weight', 'bold' | font_face: font_display: 'swap' }}
  {{ settings.type_primary_font | font_modify: 'weight', 'bold' | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}
  {{ settings.type_primary_font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}

  :root {
    --font-primary--family: {{ settings.type_primary_font.family }}, {{ settings.type_primary_font.fallback_families }};
    --font-primary--style: {{ settings.type_primary_font.style }};
    --font-primary--weight: {{ settings.type_primary_font.weight }};
    --page-width: {{ settings.max_page_width }};
    --page-margin: {{ settings.min_page_margin }}px;
    --style-border-radius-inputs: {{ settings.input_corner_radius }}px;
  }

  {% # Color scheme variables for each scheme %}
  {% for scheme in settings.color_schemes %}
    .color-{{ scheme.id }} {
      --color-background: {{ scheme.settings.background }};
      --color-background-rgb: {{ scheme.settings.background | color_to_rgb | remove: 'rgb(' | remove: ')' }};
      {% if scheme.settings.background_gradient != blank %}
        --gradient-background: {{ scheme.settings.background_gradient }};
      {% else %}
        --gradient-background: {{ scheme.settings.background }};
      {% endif %}
      --color-foreground: {{ scheme.settings.text }};
      --color-foreground-rgb: {{ scheme.settings.text | color_to_rgb | remove: 'rgb(' | remove: ')' }};
      --color-foreground-75: {{ scheme.settings.text | color_modify: 'alpha', 0.75 }};
      --color-accent-1: {{ scheme.settings.accent_1 }};
      --color-accent-1-rgb: {{ scheme.settings.accent_1 | color_to_rgb | remove: 'rgb(' | remove: ')' }};
      --color-accent-2: {{ scheme.settings.accent_2 }};
      --color-accent-2-rgb: {{ scheme.settings.accent_2 | color_to_rgb | remove: 'rgb(' | remove: ')' }};
      --color-button-text: {{ scheme.settings.outline_button_label }};
      --color-button-text-rgb: {{ scheme.settings.outline_button_label | color_to_rgb | remove: 'rgb(' | remove: ')' }};
      --color-button: {{ scheme.settings.solid_button_background }};
      --color-button-rgb: {{ scheme.settings.solid_button_background | color_to_rgb | remove: 'rgb(' | remove: ')' }};
      --color-button-label: {{ scheme.settings.solid_button_label }};
      --color-button-label-rgb: {{ scheme.settings.solid_button_label | color_to_rgb | remove: 'rgb(' | remove: ')' }};
    }
  {% endfor %}

  {% # Default color scheme for body %}
  {% assign default_scheme = settings.color_schemes | first %}
  .color-background-1 {
    --color-background: {{ default_scheme.settings.background }};
    --color-background-rgb: {{ default_scheme.settings.background | color_to_rgb | remove: 'rgb(' | remove: ')' }};
    {% if default_scheme.settings.background_gradient != blank %}
      --gradient-background: {{ default_scheme.settings.background_gradient }};
    {% else %}
      --gradient-background: {{ default_scheme.settings.background }};
    {% endif %}
    --color-foreground: {{ default_scheme.settings.text }};
    --color-foreground-rgb: {{ default_scheme.settings.text | color_to_rgb | remove: 'rgb(' | remove: ')' }};
    --color-foreground-75: {{ default_scheme.settings.text | color_modify: 'alpha', 0.75 }};
    --color-accent-1: {{ default_scheme.settings.accent_1 }};
    --color-accent-1-rgb: {{ default_scheme.settings.accent_1 | color_to_rgb | remove: 'rgb(' | remove: ')' }};
    --color-accent-2: {{ default_scheme.settings.accent_2 }};
    --color-accent-2-rgb: {{ default_scheme.settings.accent_2 | color_to_rgb | remove: 'rgb(' | remove: ')' }};
    --color-button-text: {{ default_scheme.settings.outline_button_label }};
    --color-button-text-rgb: {{ default_scheme.settings.outline_button_label | color_to_rgb | remove: 'rgb(' | remove: ')' }};
    --color-button: {{ default_scheme.settings.solid_button_background }};
    --color-button-rgb: {{ default_scheme.settings.solid_button_background | color_to_rgb | remove: 'rgb(' | remove: ')' }};
    --color-button-label: {{ default_scheme.settings.solid_button_label }};
    --color-button-label-rgb: {{ default_scheme.settings.solid_button_label | color_to_rgb | remove: 'rgb(' | remove: ')' }};
  }
{% endstyle %}
