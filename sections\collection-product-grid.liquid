{%- comment -%}
  Collection Product Grid Section
  
  Purpose: Main collection page with Shopify filtering, sorting, and pagination
  - Native Shopify Search & Discovery integration
  - Responsive product grid with customizable image ratios
  - Promo blocks can be inserted after specified product positions
  - Performance optimized with lazy loading and responsive images
  - Accessible with proper headings and ARIA labels
{%- endcomment -%}

{% liquid
  assign products_per_page = section.settings.page_size | default: 24
  assign image_ratio = section.settings.image_ratio | default: 'adapt'
  assign show_vendor = section.settings.show_vendor | default: false
  assign color_scheme = section.settings.color_scheme | default: 'scheme-1'
  
  comment
    Calculate responsive grid columns
  endcomment
  assign cols_desktop = section.settings.columns_desktop | default: 4
  assign cols_tablet = section.settings.columns_tablet | default: 2
  assign cols_mobile = section.settings.columns_mobile | default: 1
%}

<div class="collection-product-grid color-{{ color_scheme }} section-{{ section.id }}-padding">
  <div class="collection-product-grid__inner page-width">
    
    {% comment %} Collection Header {% endcomment %}
    <div class="collection-product-grid__header">
      <h1 class="collection-product-grid__title h1">{{ collection.title }}</h1>
      {% if collection.description != blank %}
        <div class="collection-product-grid__description rte">
          {{ collection.description }}
        </div>
      {% endif %}
    </div>

    {% comment %} Filter and Sort Bar {% endcomment %}
    {% if section.settings.enable_filtering or section.settings.enable_sorting %}
      <div class="collection-product-grid__toolbar">
        {% if section.settings.enable_filtering and collection.filters != empty %}
          <button 
            class="collection-product-grid__filter-toggle"
            aria-expanded="false"
            aria-controls="CollectionFilters-{{ section.id }}"
          >
            Filter
          </button>
        {% endif %}
        
        {% if section.settings.enable_sorting %}
          <div class="collection-product-grid__sort">
            <label for="SortBy-{{ section.id }}" class="visually-hidden">Sort by</label>
            <select 
              id="SortBy-{{ section.id }}"
              class="collection-product-grid__sort-select"
              onchange="window.location.href = this.value"
            >
              {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
              {%- for option in collection.sort_options -%}
                <option 
                  value="{{ option.value | escape }}"
                  {%- if option.value == sort_by %} selected="selected"{% endif -%}
                >
                  {{ option.name | escape }}
                </option>
              {%- endfor -%}
            </select>
          </div>
        {% endif %}
      </div>
    {% endif %}

    {% comment %} Filters Panel {% endcomment %}
    {% if section.settings.enable_filtering and collection.filters != empty %}
      <div class="collection-product-grid__filters" id="CollectionFilters-{{ section.id }}">
        {% render 'facets', 
          results: collection, 
          enable_filtering: true,
          enable_sorting: false,
          filter_type: 'horizontal',
          section_id: section.id
        %}
      </div>
    {% endif %}

    {% comment %} Product Grid with Pagination {% endcomment %}
    {% paginate collection.products by products_per_page %}
      <div 
        class="collection-product-grid__grid"
        style="
          --cols-desktop: {{ cols_desktop }};
          --cols-tablet: {{ cols_tablet }};
          --cols-mobile: {{ cols_mobile }};
        "
      >
        {% liquid
          assign product_index = 0
          for product in collection.products
            assign product_index = product_index | plus: 1
            
            comment
              Check for promo blocks to insert
            endcomment
            for block in section.blocks
              if block.type == 'promo' and block.settings.insert_after == product_index
                echo '<div class="collection-product-grid__promo-block" ' | append: block.shopify_attributes | append: '>'
                if block.settings.promo_type == 'image' and block.settings.image != blank
                  echo '<div class="collection-product-grid__promo-image">'
                  echo block.settings.image | image_url: width: 600 | image_tag: loading: 'lazy', class: 'collection-product-grid__promo-img', widths: '300, 400, 500, 600', alt: block.settings.image.alt
                  echo '</div>'
                elsif block.settings.promo_type == 'richtext' and block.settings.html != blank
                  echo '<div class="collection-product-grid__promo-content rte">'
                  echo block.settings.html
                  echo '</div>'
                endif
                echo '</div>'
              endif
            endfor
            
            comment
              Render product card
            endcomment
            render 'card-product', card_product: product, image_ratio: image_ratio, show_vendor: show_vendor, show_secondary_image: section.settings.show_secondary_image, show_quick_add: section.settings.show_quick_add, section_id: section.id
          endfor
        %}
      </div>

      {% comment %} Pagination {% endcomment %}
      {% if paginate.pages > 1 %}
        <div class="collection-product-grid__pagination">
          {% render 'pagination', paginate: paginate %}
        </div>
      {% endif %}

    {% endpaginate %}
  </div>
</div>

{% stylesheet %}
  .collection-product-grid {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .collection-product-grid__inner {
    max-width: var(--page-width);
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .collection-product-grid__header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .collection-product-grid__title {
    font-size: clamp(2.4rem, 4vw, 4.8rem);
    margin-bottom: 1rem;
  }

  .collection-product-grid__description {
    max-width: 60rem;
    margin: 0 auto;
    font-size: 1.6rem;
    line-height: 1.6;
    color: rgba(var(--color-foreground), 0.8);
  }

  .collection-product-grid__toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 2rem;
  }

  .collection-product-grid__filter-toggle {
    background: rgb(var(--color-background));
    border: 0.1rem solid rgba(var(--color-foreground), 0.2);
    color: rgb(var(--color-foreground));
    padding: 1rem 2rem;
    border-radius: 0.4rem;
    cursor: pointer;
    font-size: 1.4rem;
    transition: all 0.2s ease;
  }

  .collection-product-grid__filter-toggle:hover {
    border-color: rgb(var(--color-foreground));
  }

  .collection-product-grid__sort {
    position: relative;
  }

  .collection-product-grid__sort-select {
    background: rgb(var(--color-background));
    border: 0.1rem solid rgba(var(--color-foreground), 0.2);
    color: rgb(var(--color-foreground));
    padding: 1rem 3rem 1rem 1.5rem;
    border-radius: 0.4rem;
    font-size: 1.4rem;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3e%3c/path%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1.6rem;
  }

  .collection-product-grid__filters {
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(var(--color-foreground), 0.02);
    border-radius: 0.8rem;
    border: 0.1rem solid rgba(var(--color-foreground), 0.08);
  }

  .collection-product-grid__grid {
    display: grid;
    grid-template-columns: repeat(var(--cols-mobile), 1fr);
    gap: 2rem;
    margin-bottom: 4rem;
  }

  .collection-product-grid__promo-block {
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    background: rgba(var(--color-foreground), 0.02);
    border-radius: 1rem;
    text-align: center;
  }

  .collection-product-grid__promo-image {
    max-width: 100%;
  }

  .collection-product-grid__promo-img {
    width: 100%;
    height: auto;
    border-radius: 0.8rem;
  }

  .collection-product-grid__promo-content {
    max-width: 60rem;
  }

  .collection-product-grid__promo-content h2,
  .collection-product-grid__promo-content h3 {
    margin-bottom: 1rem;
  }

  .collection-product-grid__promo-content p {
    margin-bottom: 1.5rem;
    font-size: 1.6rem;
    line-height: 1.6;
  }

  .collection-product-grid__pagination {
    display: flex;
    justify-content: center;
  }

  @media screen and (min-width: 750px) {
    .collection-product-grid__grid {
      grid-template-columns: repeat(var(--cols-tablet), 1fr);
    }

    .collection-product-grid__toolbar {
      margin-bottom: 3rem;
    }
  }

  @media screen and (min-width: 990px) {
    .collection-product-grid {
      padding-top: 5rem;
      padding-bottom: 5rem;
    }

    .collection-product-grid__grid {
      grid-template-columns: repeat(var(--cols-desktop), 1fr);
    }

    .collection-product-grid__header {
      margin-bottom: 6rem;
    }
  }

  @media screen and (max-width: 749px) {
    .collection-product-grid__toolbar {
      flex-direction: column;
      gap: 1rem;
    }

    .collection-product-grid__filter-toggle,
    .collection-product-grid__sort-select {
      width: 100%;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Collection product grid",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "page_size",
      "min": 8,
      "max": 48,
      "step": 4,
      "default": 24,
      "label": "Products per page"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "Desktop columns"
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 2,
      "label": "Tablet columns"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Mobile columns",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ],
      "default": "1"
    },
    {
      "type": "header",
      "content": "Product cards"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "Image ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to image"
        },
        {
          "value": "square",
          "label": "Square (1:1)"
        },
        {
          "value": "portrait",
          "label": "Portrait (3:4)"
        }
      ],
      "default": "adapt"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "Show second product image on hover"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "Show product vendor"
    },
    {
      "type": "checkbox",
      "id": "show_quick_add",
      "default": false,
      "label": "Show quick add"
    },
    {
      "type": "header",
      "content": "Filtering and sorting"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "Enable filtering",
      "info": "Uses Shopify's Search & Discovery app"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "Enable sorting"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    }
  ],
  "blocks": [
    {
      "type": "promo",
      "name": "Promo block",
      "settings": [
        {
          "type": "select",
          "id": "promo_type",
          "label": "Content type",
          "options": [
            {
              "value": "image",
              "label": "Image"
            },
            {
              "value": "richtext",
              "label": "Rich text"
            }
          ],
          "default": "image"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "richtext",
          "id": "html",
          "label": "Content"
        },
        {
          "type": "range",
          "id": "insert_after",
          "label": "Insert after product #",
          "min": 1,
          "max": 50,
          "step": 1,
          "default": 6,
          "info": "The promo block will appear after this product number"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Collection product grid"
    }
  ]
}
{% endschema %}