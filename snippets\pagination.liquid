{%- comment -%}
  Pagination Snippet for Collection Pages
  Renders previous/next navigation and page numbers
{%- endcomment -%}

{%- if paginate.pages > 1 -%}
  <nav class="pagination" role="navigation" aria-label="Pagination">
    <ul class="pagination__list">
      {%- if paginate.previous -%}
        <li class="pagination__item pagination__item--prev">
          <a href="{{ paginate.previous.url }}" class="pagination__link" aria-label="Previous page">
            <span aria-hidden="true">&larr;</span>
            Previous
          </a>
        </li>
      {%- endif -%}

      {%- for part in paginate.parts -%}
        {%- if part.is_link -%}
          <li class="pagination__item">
            <a href="{{ part.url }}" class="pagination__link" aria-label="Page {{ part.title }}">
              {{ part.title }}
            </a>
          </li>
        {%- else -%}
          {%- if part.title == paginate.current_page -%}
            <li class="pagination__item pagination__item--current">
              <span class="pagination__link pagination__link--current" aria-current="page" aria-label="Current page, page {{ part.title }}">
                {{ part.title }}
              </span>
            </li>
          {%- else -%}
            <li class="pagination__item">
              <span class="pagination__text">{{ part.title }}</span>
            </li>
          {%- endif -%}
        {%- endif -%}
      {%- endfor -%}

      {%- if paginate.next -%}
        <li class="pagination__item pagination__item--next">
          <a href="{{ paginate.next.url }}" class="pagination__link" aria-label="Next page">
            Next
            <span aria-hidden="true">&rarr;</span>
          </a>
        </li>
      {%- endif -%}
    </ul>
  </nav>

  <style>
    .pagination {
      margin-top: 2rem;
      text-align: center;
    }
    
    .pagination__list {
      display: inline-flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 0.5rem;
      align-items: center;
    }
    
    .pagination__item {
      margin: 0;
    }
    
    .pagination__link {
      display: block;
      padding: 0.5rem 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      text-decoration: none;
      color: inherit;
      transition: all 0.2s ease;
    }
    
    .pagination__link:hover {
      background-color: #f5f5f5;
      border-color: #ccc;
    }
    
    .pagination__link--current {
      background-color: #000;
      color: white;
      border-color: #000;
    }
    
    .pagination__text {
      padding: 0.5rem 0.25rem;
      color: #999;
    }
  </style>
{%- endif -%}
