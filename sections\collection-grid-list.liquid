<section class="collection-list-grid">
  <div class="page-width">
    {% if section.settings.heading != blank %}<h2 class="h2">{{ section.settings.heading | escape }}</h2>{% endif %}

    {% assign items = '' | split: ',' %}
    {% if section.settings.mode == 'auto' %}
      {% assign count = section.settings.count | default: 6 %}
      {% assign current_handle = collection.handle %}
      {% assign all = collections | map: 'handle' %}
      {% for h in all %}
        {% if items.size >= count %}{% break %}{% endif %}
        {% assign coll = collections[h] %}
        {% if coll and coll.all_products_count > 0 %}
          {% if section.settings.exclude_current and coll.handle == current_handle %}{% continue %}{% endif %}
          {% assign items = items | push: coll.handle %}
        {% endif %}
      {% endfor %}
    {% else %}
      {% for block in section.blocks %}
        {% if block.type == 'pick' and block.settings.collection != blank %}
          {% assign items = items | push: block.settings.collection %}
        {% endif %}
      {% endfor %}
    {% endif %}

    {% if items.size > 0 %}
      <div class="clg-grid">
        {% for h in items %}
          {% assign c = collections[h] %}
          {% if c %}
            <a class="clg-card" href="{{ c.url }}">
              {% if c.featured_image %}
                {{ c.featured_image | image_url: width: 900 | image_tag: loading: 'lazy', decoding: 'async', widths: '450,600,900', sizes: '(min-width: 990px) 25vw, 50vw' }}
              {% endif %}
              <div class="clg-card__meta">
                <p class="clg-card__title">{{ c.title }}</p>
                {% if c.description != blank %}<p class="clg-card__desc">{{ c.description | strip_html | truncatewords: 16 }}</p>{% endif %}
              </div>
            </a>
          {% endif %}
        {% endfor %}
      </div>
    {% else %}
      {% if request.design_mode %}
        {% if section.settings.mode == 'manual' %}
          <p>Click "Add collection" below to select collections to display.</p>
        {% else %}
          <p>No collections found to display automatically.</p>
        {% endif %}
      {% endif %}
    {% endif %}
  </div>
  <style>
    .clg-grid { display:grid; gap:1rem; grid-template-columns: repeat(2,minmax(0,1fr)); }
    @media (min-width: 750px){ .clg-grid { grid-template-columns: repeat(3,1fr); } }
    @media (min-width: 1200px){ .clg-grid { grid-template-columns: repeat(4,1fr); } }
    .clg-card { display:flex; flex-direction:column; gap:.5rem; text-decoration:none; color:inherit; }
    .clg-card__title { font-weight:600; }
    .clg-card__desc { opacity:.75; font-size:.9rem; }
  </style>
</section>

{% schema %}
{
  "name": "Collection list (grid)",
  "enabled_on": { "templates": ["collection"] },
  "settings": [
    { "type":"text", "id":"heading", "label":"Heading" },
    { "type":"select", "id":"mode", "label":"Mode", "default":"manual", "options":[
      { "value":"manual", "label":"Manual (pick collections)" },
      { "value":"auto", "label":"Automatic (list collections)" }
    ]},
    { "type":"checkbox", "id":"exclude_current", "label":"Exclude current collection (auto mode)", "default": true },
    { "type":"range", "id":"count", "label":"Max to show (auto mode)", "min": 2, "max": 24, "step": 1, "default": 8 }
  ],
  "blocks": [
    { "type":"pick", "name":"Collection", "settings":[ { "type":"collection", "id":"collection", "label":"Collection" } ] }
  ],
  "presets": [{
    "name":"Collection list (grid)",
    "blocks": [
      { "type": "pick" },
      { "type": "pick" },
      { "type": "pick" }
    ]
  }]
}
{% endschema %}
