{"404": {"title": "404", "not_found": "Page not found.", "back_to_shopping": "Back to shopping"}, "blog": {"article_comments": "Comments", "article_metadata_html": "{{ date }} by {{ author }}", "comment_form_body": "Comment", "comment_form_email": "Email", "comment_form_name": "Name", "comment_form_submit": "Post", "comment_form_title": "Add a comment"}, "cart": {"checkout": "Checkout", "title": "<PERSON><PERSON>", "update": "Update", "remove": "Remove", "item_count": "items in cart"}, "customers": {"login": {"email": "Email", "password": "Password", "submit": "Sign in", "title": "<PERSON><PERSON>"}}, "collections": {"title": "Collections", "general": {"no_matches": "No products match your search", "no_products_html": "There are no products in this collection yet."}}, "gift_card": {"add_to_apple_wallet": "Add to Apple Wallet", "card": "Gift card", "expired": "This gift card has expired", "expires_on": "Expires on {{ expires_on }}", "use_at_checkout": "Use this gift card at checkout"}, "password": {"title": "This shop is private", "password": "Password", "enter": "Enter"}, "search": {"title": "Search", "placeholder": "Search articles, pages, or products", "submit": "Search", "no_results_html": "No results found for {{ terms }}", "results_for_html": "{{ count }} results found for {{ terms }}"}, "products": {"product": {"on_sale": "Sale", "add_to_cart": "Add to cart", "sold_out": "Sold out", "choose_options": "Choose options", "quick_add": "Quick add", "price": {"from_price_html": "from {{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}}}, "accessibility": {"unit_price_separator": "per", "close": "Close"}, "general": {"close": "Close", "typography": "Typography", "fonts": "Fonts", "primary": "Primary font", "layout": "Layout", "colors": "Colors", "color_schemes": "Color schemes", "header": {"logo": "Logo", "logo_width": "Logo width (px)", "logo_mobile": "Mobile logo"}, "announcement": {"title": "Announcement", "close": "Close announcement", "go_to_announcement": "Go to announcement"}, "cart": {"item_count": "items in cart"}}, "settings_schema": {"colors": {"settings": {"background": {"label": "Background"}, "background_gradient": {"label": "Background gradient"}, "text": {"label": "Text"}, "accent_1": {"label": "Accent 1"}, "accent_2": {"label": "Accent 2"}, "outline_button_label": {"label": "Outline button"}, "solid_button_background": {"label": "Solid button background"}, "solid_button_label": {"label": "Solid button label"}}}}, "labels": {"page_width": "Page width", "page_margin": "Page margin", "background": "Background", "foreground": "Foreground", "input_corner_radius": "Input corner radius"}, "options": {"page_width": {"narrow": "Narrow (1440px)", "wide": "Wide (1760px)"}}}