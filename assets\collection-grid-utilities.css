/* Collection Grid Utilities */
.product-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(2, 1fr); /* Default: 2 columns on mobile */
}

/* Mobile column overrides */
.grid--1-col-tablet {
  grid-template-columns: repeat(1, 1fr);
}

.grid--2-col-tablet {
  grid-template-columns: repeat(2, 1fr);
}

/* Tablet breakpoint - inherit mobile or use desktop settings */
@media screen and (min-width: 750px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr); /* Default 2 columns on tablet */
  }
  
  .grid--1-col-tablet {
    grid-template-columns: repeat(1, 1fr);
  }
  
  .grid--2-col-tablet {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop breakpoint - use desktop settings */
@media screen and (min-width: 990px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr); /* Default 4 columns on desktop */
  }
  
  .grid--2-col-desktop {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid--3-col-desktop {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid--4-col-desktop {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Spacing utilities */
.section-padding {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media screen and (max-width: 749px) {
  .section-padding {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

/* Accessibility utilities */
.visually-hidden {
  position: absolute !important;
  overflow: hidden;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  clip: rect(0 0 0 0);
  word-wrap: normal !important;
}

/* Select component */
.select {
  position: relative;
  display: inline-block;
}

.select__select {
  background: rgb(var(--color-background));
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
  border-radius: var(--border-radius);
  padding: 1rem 3rem 1rem 1.5rem;
  font-size: 1.4rem;
  color: rgb(var(--color-foreground));
  appearance: none;
  min-width: 20rem;
  cursor: pointer;
  transition: border-color 0.15s ease;
}

.select__select:hover,
.select__select:focus {
  border-color: rgb(var(--color-foreground));
  outline: none;
}

.select svg {
  position: absolute;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
  pointer-events: none;
  width: 1.2rem;
  height: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
}

/* Active facets */
.active-facets {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
}

.active-facets__button-wrapper {
  display: inline-block;
}

.active-facets__button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

.active-facets__button-inner {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(var(--color-foreground), 0.08);
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
  border-radius: var(--border-radius);
  font-size: 1.2rem;
  color: rgb(var(--color-foreground));
  text-decoration: none;
  transition: all 0.15s ease;
}

.active-facets__button-inner:hover {
  background: rgba(var(--color-foreground), 0.12);
  border-color: rgb(var(--color-foreground));
}

.active-facets__button-remove {
  color: rgba(var(--color-foreground), 0.7);
  font-size: 1.2rem;
  padding: 0.5rem;
  text-decoration: underline;
}

.active-facets__button-remove:hover {
  color: rgb(var(--color-foreground));
}

/* Product count */
.product-count-vertical {
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.7);
}

/* Template search */
.template-search__results-empty {
  text-align: center;
  padding: 4rem 0;
}

.template-search__results-empty h2 {
  margin-bottom: 1rem;
}

.template-search__results-empty p {
  color: rgba(var(--color-foreground), 0.7);
}

/* Media component */
.media {
  display: block;
  position: relative;
  overflow: hidden;
}

.media--transparent {
  background: transparent;
}

.motion-reduce {
  transition: none !important;
}

/* Full width utility */
.full-width {
  width: 100vw;
  margin-left: calc((100vw - 100%) / -2);
}

/* Button utilities */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1.2rem 2.4rem;
  background: rgb(var(--color-button, var(--color-accent-1)));
  color: rgb(var(--color-button-text, var(--color-base-accent-1)));
  border: 0.1rem solid rgb(var(--color-button, var(--color-accent-1)));
  border-radius: var(--border-radius);
  font-size: 1.4rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease;
  min-height: 4.4rem;
}

.button:hover {
  opacity: 0.85;
}

.button--secondary {
  background: transparent;
  color: rgb(var(--color-foreground));
  border-color: rgba(var(--color-foreground), 0.2);
}

.button--secondary:hover {
  background: rgba(var(--color-foreground), 0.08);
  border-color: rgb(var(--color-foreground));
  opacity: 1;
}

.button--tertiary {
  background: transparent;
  color: rgb(var(--color-foreground));
  border: none;
  padding: 0.5rem 1rem;
  min-height: 3rem;
}

.button--full-width {
  width: 100%;
}

/* Underline links */
.underlined-link {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

.underlined-link:hover {
  text-decoration-thickness: 0.2rem;
}

.underline-links-hover a:hover {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

/* Card utilities */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card__information {
  flex: 1;
}

.card__heading {
  margin: 0;
}

.card__heading a {
  color: inherit;
  text-decoration: none;
}

.full-unstyled-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.full-unstyled-link:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
}

/* Badge */
.badge {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  font-size: 1.1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  border-radius: var(--border-radius);
}

.badge--bottom-left {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  z-index: 2;
}

/* Price component */
.price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 1.3rem;
}

.price--on-sale .price__regular {
  color: rgba(var(--color-foreground), 0.5);
  text-decoration: line-through;
}

.price__sale {
  color: rgb(var(--color-sale-text, var(--color-foreground)));
  font-weight: 500;
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-background), 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-overlay.hidden {
  display: none;
}

.loading-overlay__spinner {
  width: 2rem;
  height: 2rem;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* No JS fallback */
.no-js-hidden {
  display: block;
}

.no-js .no-js-hidden {
  display: none !important;
}
