{%- comment -%}
  Section: Social Media Highlights
  Purpose: Display UGC via Instagram/UGC apps (App Blocks) or manual, curated posts.
  Notes:
  - No external libraries. <PERSON><PERSON><PERSON> uses CSS Scroll Snap with optional vanilla JS enhancement.
  - Inherits theme color schemes; no hardcoded colors.
  - Accessibility: decorative images default to empty alt; distinct overlay links for post/product (no nested anchors).
  - Compliant with Online Store 2.0, Theme Check, and Dawn-style conventions.
{%- endcomment -%}

{{ 'section-social-highlights.css' | asset_url | stylesheet_tag }}
<script src="{{ 'section-social-highlights.js' | asset_url }}" defer="defer"></script>

<section
  id="SocialHighlights-{{ section.id }}"
  class="social-highlights color-{{ section.settings.color_scheme }} social-highlights--layout-{{ section.settings.layout }}"
  role="region"
  aria-label="{{ section.settings.heading | strip_html | escape | default: 'Social media highlights' }}"
>
  {%- if section.settings.heading != blank or section.settings.subheading != blank -%}
    <div class="page-width social-highlights__header">
      {%- if section.settings.heading != blank -%}
        <h2 class="social-highlights__heading">{{ section.settings.heading | escape }}</h2>
      {%- endif -%}
      {%- if section.settings.subheading != blank -%}
        <p class="social-highlights__subheading">{{ section.settings.subheading | escape }}</p>
      {%- endif -%}
    </div>
  {%- endif -%}

  <div class="social-highlights__container">
    {%- if section.settings.content_source == 'app' -%}
      {%- comment -%} Dedicated area for App Blocks (e.g., Instafeed, Covet, Foursixty) {%- endcomment -%}
      {%- if section.blocks.size > 0 -%}
        <div class="page-width social-highlights__app">
          {%- for block in section.blocks -%}
            {%- if block.type == '@app' -%}
              <div class="social-highlights__app-block" {{ block.shopify_attributes }}>
                {% render block %}
              </div>
            {%- endif -%}
          {%- endfor -%}
        </div>
      {%- else -%}
        <div class="page-width social-highlights__placeholder">
          <p>Add an Instagram/UGC app block to display your feed.</p>
        </div>
      {%- endif -%}
    {%- else -%}
      {%- assign grid_limit = section.settings.images_per_row | times: section.settings.rows -%}
      {%- if section.settings.layout == 'grid' -%}
        <div
          class="social-highlights__list social-highlights__list--grid page-width"
          style="--sh-cols: {{ section.settings.images_per_row | default: 4 }};"
          data-layout="grid"
        >
          {%- if section.blocks.size > 0 -%}
            {%- for block in section.blocks limit: grid_limit -%}
              {%- if block.type == 'social_post' -%}
                <article class="social-post" {{ block.shopify_attributes }}>
                  <div class="social-post__media">
                    {%- if block.settings.image != blank -%}
                      {%- assign sizes_value = '(min-width: 1200px) calc(100% / ' | append: section.settings.images_per_row | default: 4 | append: '), 100vw' -%}
                      {{ block.settings.image
                        | image_url: width: 1500
                        | image_tag:
                          widths: '400, 600, 800, 1000, 1200, 1500',
                          sizes: sizes_value,
                          class: 'social-post__image',
                          alt: block.settings.alt_text | default: '',
                          loading: 'lazy',
                          decoding: 'async'
                      }}
                    {%- endif -%}

                    {%- if block.settings.post_link != blank -%}
                      <a
                        class="social-post__taparea"
                        href="{{ block.settings.post_link }}"
                        target="_blank"
                        rel="noopener"
                        aria-label="Open original post"
                      ></a>
                    {%- endif -%}

                    {%- if block.settings.tagged_product != blank -%}
                      <a
                        class="social-post__hotspot"
                        href="{{ block.settings.tagged_product.url }}"
                        aria-label="Shop {{ block.settings.tagged_product.title | escape }}"
                      >
                        <svg class="social-post__hotspot-icon" width="18" height="18" viewBox="0 0 24 24" aria-hidden="true">
                          <path d="M12 4a8 8 0 100 16 8 8 0 000-16zm1 4v3h3v2h-3v3h-2v-3H8v-2h3V8h2z" fill="currentColor"/>
                        </svg>
                        <span class="visually-hidden">Shop product</span>
                      </a>
                    {%- endif -%}
                  </div>
                </article>
              {%- endif -%}
            {%- endfor -%}
          {%- else -%}
            <div class="page-width social-highlights__placeholder">
              <p>Add Social post blocks to populate this section.</p>
            </div>
          {%- endif -%}
        </div>
      {%- else -%}
        <div
          class="social-highlights__list social-highlights__list--slider"
          data-layout="slider"
          aria-label="UGC slider"
        >
          {%- if section.blocks.size > 0 -%}
            {%- for block in section.blocks -%}
              {%- if block.type == 'social_post' -%}
                <article class="social-post" {{ block.shopify_attributes }}>
                  <div class="social-post__media">
                    {%- if block.settings.image != blank -%}
                      {{ block.settings.image
                        | image_url: width: 1500
                        | image_tag:
                          widths: '400, 600, 800, 1000, 1200, 1500',
                          sizes: '90vw',
                          class: 'social-post__image',
                          alt: block.settings.alt_text | default: '',
                          loading: 'lazy',
                          decoding: 'async'
                      }}
                    {%- endif -%}

                    {%- if block.settings.post_link != blank -%}
                      <a
                        class="social-post__taparea"
                        href="{{ block.settings.post_link }}"
                        target="_blank"
                        rel="noopener"
                        aria-label="Open original post"
                      ></a>
                    {%- endif -%}

                    {%- if block.settings.tagged_product != blank -%}
                      <a
                        class="social-post__hotspot"
                        href="{{ block.settings.tagged_product.url }}"
                        aria-label="Shop {{ block.settings.tagged_product.title | escape }}"
                      >
                        <svg class="social-post__hotspot-icon" width="18" height="18" viewBox="0 0 24 24" aria-hidden="true">
                          <path d="M12 4a8 8 0 100 16 8 8 0 000-16zm1 4v3h3v2h-3v3h-2v-3H8v-2h3V8h2z" fill="currentColor"/>
                        </svg>
                        <span class="visually-hidden">Shop product</span>
                      </a>
                    {%- endif -%}
                  </div>
                </article>
              {%- endif -%}
            {%- endfor -%}
          {%- else -%}
            <div class="page-width social-highlights__placeholder">
              <p>Add Social post blocks to populate this section.</p>
            </div>
          {%- endif -%}
        </div>

        {%- if section.settings.show_arrows -%}
          <div class="sh-slider__arrows" aria-hidden="true">
            <button type="button" class="sh-slider__arrow sh-slider__arrow--prev" aria-label="Previous">
              {% render 'icon-caret' %}
            </button>
            <button type="button" class="sh-slider__arrow sh-slider__arrow--next" aria-label="Next">
              {% render 'icon-caret' %}
            </button>
          </div>
        {%- endif -%}
        {%- if section.settings.show_dots -%}
          <div class="sh-slider__dots" aria-hidden="true"></div>
        {%- endif -%}
      {%- endif -%}
    {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Social media highlights",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "select",
      "id": "content_source",
      "label": "Content source",
      "default": "app",
      "options": [
        { "value": "app", "label": "Instagram/UGC app (recommended)" },
        { "value": "manual", "label": "Manual curation" }
      ],
      "info": "Use an App Block for Instagram/UGC feeds, or manually curate images without an app."
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Shop our Instagram"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Tag us @yourbrand #yourbrand to be featured"
    },
    {
      "type": "radio",
      "id": "layout",
      "label": "Layout",
      "default": "grid",
      "options": [
        { "value": "grid", "label": "Grid" },
        { "value": "slider", "label": "Slider" }
      ]
    },
    {
      "type": "range",
      "id": "images_per_row",
      "min": 3,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "Images per row",
      "info": "Applies to grid layout."
    },
    {
      "type": "range",
      "id": "rows",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 2,
      "label": "Number of rows",
      "info": "Applies to grid layout."
    },
    {
      "type": "checkbox",
      "id": "show_arrows",
      "default": true,
      "label": "Show navigation arrows",
      "info": "Applies to slider layout."
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "default": true,
      "label": "Show dot indicators",
      "info": "Applies to slider layout."
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme-1",
      "label": "Color scheme"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "social_post",
      "name": "Social post",
      "limit": 24,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "alt_text",
          "label": "Alt text",
          "info": "Leave blank for decorative images."
        },
        {
          "type": "url",
          "id": "post_link",
          "label": "Original post link"
        },
        {
          "type": "product",
          "id": "tagged_product",
          "label": "Tagged product (optional)"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Social media highlights",
      "blocks": []
    }
  ]
}
{% endschema %}
