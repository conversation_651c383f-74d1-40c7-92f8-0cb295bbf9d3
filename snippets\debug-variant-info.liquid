{%- comment -%}
  Debug snippet to show variant information
  Add this to your product page temporarily to see variant setup
{%- endcomment -%}

<div style="background: #f0f0f0; padding: 20px; margin: 20px 0; border: 1px solid #ccc;">
  <h3>🔧 Debug: Variant Information</h3>
  
  <p><strong>Product:</strong> {{ product.title }}</p>
  <p><strong>Total Variants:</strong> {{ product.variants.size }}</p>
  <p><strong>Has variants:</strong> {{ product.has_only_default_variant | default: false }}</p>
  
  <h4>Options:</h4>
  {%- for option in product.options_with_values -%}
    <div style="margin: 10px 0; padding: 10px; background: white;">
      <strong>{{ option.name }}:</strong>
      {%- for value in option.values -%}
        <span style="margin: 0 5px; padding: 2px 8px; background: #e0e0e0;">{{ value }}</span>
      {%- endfor -%}
    </div>
  {%- endfor -%}
  
  <h4>Variants:</h4>
  {%- for variant in product.variants -%}
    <div style="margin: 5px 0; padding: 5px; background: white; font-size: 12px;">
      <strong>{{ variant.title }}</strong> - 
      ID: {{ variant.id }} - 
      Media: {{ variant.featured_media.id | default: 'none' }} -
      Available: {{ variant.available }}
    </div>
  {%- endfor -%}
  
  <h4>Media:</h4>
  {%- for media in product.media -%}
    <div style="margin: 5px 0; padding: 5px; background: white; font-size: 12px;">
      Media ID: {{ media.id }} - Alt: {{ media.alt }} - Type: {{ media.media_type }}
    </div>
  {%- endfor -%}
</div>
