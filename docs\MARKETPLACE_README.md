# Marketplace submission checklist

This file is a compact checklist and short report to prepare this theme for Shopify Theme Store submission.

1. Theme basics
   - [ ] Theme uses Online Store 2.0 patterns (JSON templates, `sections/`, `locales/`).
   - [ ] All templates in `templates/` are JSON templates where applicable.
   - [ ] `settings_schema.json` and `config/settings_data.json` are present and representative.

2. Code quality & tooling
   - [ ] Run `shopify theme check` and fix errors/warnings.
   - [ ] Add automated linting to CI (Theme Check + optional ESLint and CSS linters for assets).

3. Accessibility & performance
   - [ ] Run Lighthouse and fix major performance regressions (LCP/CLS/TTI).
   - [ ] Run pa11y or axe and fix high-severity accessibility issues.

4. Localization
   - [ ] Provide `locales/en.default.json` (done). Add other locales as needed.

5. Demo store & documentation
   - [ ] Provide a Partner dev store URL and reviewer access.
   - [ ] Add screenshots (desktop & mobile), short/long descriptions, and a demo link.
   - [ ] Add `README.md` user docs (installation, customizing, known limitations).

6. Marketing & support
   - [ ] Prepare support plan and contact email.
   - [ ] Provide license details and update policy.

---

Repository quick gaps report (auto-generated by a local scan):

- JSON templates: detected `index.json`, `collection.json`, `product.json`, `cart.json`, `page.json`, `list-collections.json`, `search.json`, `blog.json`, `article.json`, `404.json`, `password.json` — good OS 2.0 coverage.
- Sections: `sections/` directory contains modular sections including `header.liquid`, `footer.liquid`, and page-specific sections — good.
- Locales: `locales/en.default.json` exists — include additional localization if targeting other markets.
- Theme Check: not run in this repo yet — strongly recommended to run and fix issues before submission.

Use this file as the authoritative pre-submission checklist and update the tick boxes as items are completed. Attach screenshots and demo store credentials to your Partner submission.

---

If you'd like, I can:

- add a GitHub Actions workflow that runs Theme Check + Lighthouse + pa11y on pushes to `main`.
- update `README.md` with a concise “How to submit to Theme Store” section.

Choose one of the two and I'll implement it next.
