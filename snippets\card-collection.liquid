{%- comment -%}
  Collection Card Snippet
  - Renders collection cards with image, title, optional description
  - Responsive image handling with proper aspect ratios
  - Accessibility support with proper labels
{%- endcomment -%}

{%- liquid
  assign card_collection = card_collection
  assign media_aspect_ratio = media_aspect_ratio | default: 'square'
  assign show_description = show_description | default: false
  assign columns = columns | default: 3
  assign section_id = section_id | default: 'collection-card'

  # Calculate responsive image sizing based on grid columns
  case columns
    when 2
      assign image_width = 800
      assign widths_attr = '400, 600, 800, 1000'
      assign sizes_attr = '(min-width: 990px) 50vw, 100vw'
    when 4
      assign image_width = 600
      assign widths_attr = '300, 400, 500, 600'
      assign sizes_attr = '(min-width: 990px) calc(100vw / 4), 50vw'
    else
      assign image_width = 700
      assign widths_attr = '350, 500, 600, 700'
      assign sizes_attr = '(min-width: 990px) calc(100vw / 3), 50vw'
  endcase

  # Set aspect ratio CSS variable
  case media_aspect_ratio
    when 'portrait'
      assign aspect_ratio = '3/4'
    when 'square'
      assign aspect_ratio = '1/1'
    when 'landscape'
      assign aspect_ratio = '4/3'
    else
      assign aspect_ratio = 'auto'
  endcase
-%}

<div class="card-wrapper underline-links-hover">
  <div class="card card--collection card--media{% if card_collection.featured_image == blank %} card--placeholder{% endif %}">
    <div 
      class="card__media" 
      {% unless media_aspect_ratio == 'adapt' %}style="--aspect-ratio: {{ aspect_ratio }};"{% endunless %}
    >
      <div class="media media--transparent">
        {%- if card_collection.featured_image -%}
          {{ card_collection.featured_image
            | image_url: width: image_width
            | image_tag:
              widths: widths_attr,
              sizes: sizes_attr,
              loading: 'lazy',
              alt: card_collection.featured_image.alt | default: card_collection.title,
              class: 'motion-reduce'
          }}
        {%- else -%}
          <div class="card__media-placeholder">
            {{ 'collection-1' | placeholder_svg_tag: 'placeholder-svg' }}
          </div>
        {%- endif -%}
      </div>
    </div>
    
    <div class="card__content">
      <div class="card__information">
        <h3 class="card__heading">
          <a
            href="{{ card_collection.url }}"
            id="CollectionCardLink-{{ card_collection.id }}-{{ section_id }}"
            class="full-unstyled-link"
            aria-labelledby="CollectionCardLink-{{ card_collection.id }}-{{ section_id }} CollectionCardBadge-{{ card_collection.id }}-{{ section_id }}"
          >
            {{ card_collection.title | escape }}
          </a>
        </h3>
        
        {%- if show_description and card_collection.description != blank -%}
          <p class="card__caption">
            {{ card_collection.description | strip_html | truncatewords: 20 | escape }}
          </p>
        {%- endif -%}
        
        <div class="card__badge">
          <span 
            id="CollectionCardBadge-{{ card_collection.id }}-{{ section_id }}" 
            class="badge badge--collection"
          >
            {%- liquid
              assign products_count = card_collection.products_count
              if products_count == 1
                echo 'collections.general.items_with_count.one' | t: count: products_count
              else
                echo 'collections.general.items_with_count.other' | t: count: products_count
              endif
            -%}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .card--collection {
    position: relative;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .card--collection:hover {
    transform: translateY(-0.2rem);
    box-shadow: 0 0.4rem 1.2rem rgba(var(--color-shadow), 0.15);
  }

  .card--collection .card__media {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
  }

  .card--collection .card__media[style*="--aspect-ratio"] .media {
    aspect-ratio: var(--aspect-ratio);
  }

  .card--collection .card__media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .card--collection:hover .card__media img {
    transform: scale(1.05);
  }

  .card--collection .card__media-placeholder {
    aspect-ratio: var(--aspect-ratio, 1/1);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(var(--color-foreground), 0.04);
    border-radius: var(--border-radius);
  }

  .card--collection .card__media-placeholder svg {
    width: 4rem;
    height: 4rem;
    opacity: 0.3;
  }

  .card--collection .card__content {
    padding: 1.5rem 0 0;
  }

  .card--collection .card__heading {
    margin-bottom: 0.5rem;
  }

  .card--collection .card__heading a {
    color: rgb(var(--color-foreground));
    text-decoration: none;
    font-weight: 600;
  }

  .card--collection .card__heading a:hover {
    color: rgb(var(--color-link));
  }

  .card--collection .card__caption {
    color: rgba(var(--color-foreground), 0.7);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
  }

  .card--collection .badge--collection {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    background-color: rgba(var(--color-foreground), 0.06);
    color: rgba(var(--color-foreground), 0.7);
    border-radius: var(--border-radius);
    font-weight: 500;
  }

  @media screen and (max-width: 749px) {
    .card--collection .card__content {
      padding: 1rem 0 0;
    }
  }
</style>
