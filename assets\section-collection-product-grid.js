/**
 * Collection Product Grid
 * - Enhanced filtering with AJAX
 * - Sort functionality
 * - URL state management
 * - Loading states and accessibility
 */

class CollectionProductGrid {
  constructor() {
    this.container = document.querySelector('[data-section]');
    this.filtersForm = document.getElementById('FacetForm');
    this.productGrid = document.querySelector('[data-product-grid]');
    this.filterToggle = document.querySelector('[data-filter-toggle]');
    this.filterClose = document.querySelector('[data-filter-close]');
    this.filtersContainer = document.querySelector('[data-filters]');
    this.sortSelect = document.querySelector('[data-sort-select]');
    this.loadingOverlay = document.querySelector('[data-loading]');
    
    this.searchParams = new URLSearchParams(window.location.search);
    this.isLoading = false;
    
    this.init();
  }

  init() {
    this.attachEventListeners();
    this.setupAccessibility();
  }

  attachEventListeners() {
    // Filter form submission
    if (this.filtersForm) {
      this.filtersForm.addEventListener('change', (e) => {
        this.handleFilterChange(e);
      });

      // Price range inputs with debounce
      const priceInputs = this.filtersForm.querySelectorAll('input[type="number"]');
      priceInputs.forEach(input => {
        let timeout;
        input.addEventListener('input', () => {
          clearTimeout(timeout);
          timeout = setTimeout(() => {
            this.handleFilterChange();
          }, 1000);
        });
      });
    }

    // Sort dropdown
    if (this.sortSelect) {
      this.sortSelect.addEventListener('change', (e) => {
        this.handleSortChange(e.target.value);
      });
    }

    // Mobile filter toggle
    if (this.filterToggle) {
      this.filterToggle.addEventListener('click', () => {
        this.openMobileFilters();
      });
    }

    if (this.filterClose) {
      this.filterClose.addEventListener('click', () => {
        this.closeMobileFilters();
      });
    }

    // Mobile filters backdrop
    if (this.filtersContainer) {
      this.filtersContainer.addEventListener('click', (e) => {
        if (e.target === this.filtersContainer) {
          this.closeMobileFilters();
        }
      });
    }

    // Pagination links
    document.addEventListener('click', (e) => {
      if (e.target.closest('.pagination__item a')) {
        e.preventDefault();
        const url = e.target.href;
        this.loadPage(url);
      }
    });

    // Active filter removal
    document.addEventListener('click', (e) => {
      if (e.target.closest('.collection-product-grid__active-filter-tag')) {
        e.preventDefault();
        const url = e.target.closest('.collection-product-grid__active-filter-tag').href;
        this.loadPage(url);
      }
    });

    // Clear all filters
    document.addEventListener('click', (e) => {
      if (e.target.closest('.collection-product-grid__clear-all')) {
        e.preventDefault();
        const url = e.target.href;
        this.loadPage(url);
      }
    });

    // Keyboard navigation for mobile filters
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isMobileFiltersOpen()) {
        this.closeMobileFilters();
      }
    });
  }

  setupAccessibility() {
    // Set up proper ARIA states
    if (this.filterToggle && this.filtersContainer) {
      this.filterToggle.setAttribute('aria-controls', this.filtersContainer.id);
    }
  }

  handleFilterChange(e) {
    if (this.isLoading) return;
    
    // For non-checkbox/radio inputs, we need to handle them differently
    if (e && (e.target.type === 'number' || e.target.tagName === 'SELECT')) {
      return; // These are handled by their specific handlers
    }

    const formData = new FormData(this.filtersForm);
    const searchParams = new URLSearchParams(formData);
    
    // Preserve sort order
    if (this.sortSelect && this.sortSelect.value) {
      searchParams.set('sort_by', this.sortSelect.value);
    }

    const url = `${window.location.pathname}?${searchParams.toString()}`;
    this.loadPage(url);
  }

  handleSortChange(sortValue) {
    if (this.isLoading) return;

    const url = new URL(window.location);
    if (sortValue) {
      url.searchParams.set('sort_by', sortValue);
    } else {
      url.searchParams.delete('sort_by');
    }

    this.loadPage(url.toString());
  }

  async loadPage(url) {
    if (this.isLoading) return;

    this.isLoading = true;
    this.showLoading();

    try {
      const response = await fetch(url, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      const html = await response.text();
      const parser = new DOMParser();
      const newDocument = parser.parseFromString(html, 'text/html');

      // Update URL without page reload
      window.history.pushState({ path: url }, '', url);

      // Update the page content
      this.updatePageContent(newDocument);

      // Announce to screen readers
      this.announceUpdate();

      // Close mobile filters if open
      this.closeMobileFilters();

    } catch (error) {
      console.error('Error loading page:', error);
      // Fallback to full page reload
      window.location.href = url;
    } finally {
      this.isLoading = false;
      this.hideLoading();
    }
  }

  updatePageContent(newDocument) {
    // Update main content areas
    const sectionsToUpdate = [
      '[data-product-grid]',
      '[data-filters]',
      '.collection-product-grid__summary',
      '.collection-product-grid__sort'
    ];

    sectionsToUpdate.forEach(selector => {
      const currentElement = document.querySelector(selector);
      const newElement = newDocument.querySelector(selector);
      
      if (currentElement && newElement) {
        currentElement.innerHTML = newElement.innerHTML;
      }
    });

    // Update page title if it exists
    const newTitle = newDocument.querySelector('title');
    if (newTitle) {
      document.title = newTitle.textContent;
    }

    // Re-initialize any components that might have been replaced
    this.reinitializeComponents();
  }

  reinitializeComponents() {
    // Re-attach event listeners to new elements
    this.filtersForm = document.getElementById('FacetForm');
    this.sortSelect = document.querySelector('[data-sort-select]');
    
    // Re-initialize any other components that might be needed
    // For example, if you have other JavaScript components that need to be re-initialized
    
    // Trigger custom event for other scripts to hook into
    document.dispatchEvent(new CustomEvent('collection-grid:updated'));
  }

  openMobileFilters() {
    if (!this.filtersContainer) return;

    this.filtersContainer.setAttribute('data-open', '');
    this.filterToggle?.setAttribute('aria-expanded', 'true');
    
    // Focus management
    const firstInput = this.filtersContainer.querySelector('input, button');
    if (firstInput) {
      firstInput.focus();
    }

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  closeMobileFilters() {
    if (!this.filtersContainer) return;

    this.filtersContainer.removeAttribute('data-open');
    this.filterToggle?.setAttribute('aria-expanded', 'false');
    
    // Return focus to toggle button
    if (this.filterToggle) {
      this.filterToggle.focus();
    }

    // Restore body scroll
    document.body.style.overflow = '';
  }

  isMobileFiltersOpen() {
    return this.filtersContainer?.hasAttribute('data-open');
  }

  showLoading() {
    if (this.loadingOverlay) {
      this.loadingOverlay.hidden = false;
    }
    
    // Add loading state to product grid
    if (this.productGrid) {
      this.productGrid.setAttribute('aria-busy', 'true');
    }
  }

  hideLoading() {
    if (this.loadingOverlay) {
      this.loadingOverlay.hidden = true;
    }
    
    // Remove loading state from product grid
    if (this.productGrid) {
      this.productGrid.setAttribute('aria-busy', 'false');
    }
  }

  announceUpdate() {
    // Create temporary announcement for screen readers
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = 'Products updated';
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new CollectionProductGrid();
});

// Handle browser back/forward buttons
window.addEventListener('popstate', (e) => {
  if (e.state && e.state.path) {
    window.location.reload();
  }
});
