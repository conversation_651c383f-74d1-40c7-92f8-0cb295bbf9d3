/* variant-hover.js
   Purpose: Smooth, non-flickery color-swatch hover preview on product cards.
   Approach: decode-first, two-layer crossfade; revert on card-leave; race-safe.
   No hacks, no inline layout toggles, no external libs.
*/
(function () {
  'use strict';

  class CardHoverPreview {
    constructor(container) {
      this.container = container;
      this.baseImg = container.querySelector('.card__media .card-media__primary');
      this.previewImg = container.querySelector('.card__media .card-media__preview');

      // Ensure sold-out indicator starts hidden
      const indicator = container.querySelector('.swatch-sold-out-indicator');
      if (indicator) indicator.hidden = true;

      if (!this.baseImg || !this.previewImg) return;

      // Card link updating (optional progressive enhancement)
      this.cardLink = container.querySelector('.card-link');
      this.baseProductUrl = this.cardLink ? this.cardLink.getAttribute('href') : null;

      // Track currently selected variant for Quick Add
      this.currentVariantId = null;

      this.original = {
        src: this.baseImg.getAttribute('src') || '',
        srcset: this.baseImg.getAttribute('srcset') || '',
        sizes: this.baseImg.getAttribute('sizes') || '',
      };

      this.hoverTimer = null;
      this.requestId = 0;
      this.revertDelay =
        parseInt(container.getAttribute('data-revert-delay'), 10) ||
        parseInt(container.dataset.revertDelay, 10) ||
        80; // small grace to avoid mid-card revert

      this._bind();
    }

    _bind() {
      const pointerIsCoarse = window.matchMedia('(pointer: coarse)').matches;
      const swatches = this.container.querySelectorAll('.swatch[data-variant-id]');

      swatches.forEach((swatch) => {
        swatch.addEventListener('pointerenter', (e) => {
          if (pointerIsCoarse) return; // no hover on touch
          clearTimeout(this.hoverTimer);
          const el = e.currentTarget;
          // tiny hover-intent to avoid thrash
          this.hoverTimer = setTimeout(() => this._previewFromEl(el), 50);
        }, { passive: true });

        // Click to lock in selection for Quick Add
        swatch.addEventListener('click', (e) => {
          const el = e.currentTarget;
          this._selectVariant(el);
        });
      });

      // One revert when leaving the whole card, not each swatch
      this.container.addEventListener('pointerleave', () => {
        clearTimeout(this.hoverTimer);
        // Only revert if no variant is permanently selected
        if (!this.isVariantSelected) {
          setTimeout(() => this._revertToOriginal(), this.revertDelay);
        }
      }, { passive: true });
    }

    _selectVariant(el) {
      // Permanently select this variant for Quick Add
      this.currentVariantId = el.dataset.variantId;
      this.isVariantSelected = true;

      // Check if this variant is sold out by looking for the CSS class
      const isSoldOut = el.classList.contains('swatch--sold-out');

      // Debug logging
      console.log('Variant selected:', {
        variantId: this.currentVariantId,
        colorName: el.dataset.colorName,
        element: el,
        isSoldOut: isSoldOut,
        classList: Array.from(el.classList)
      });

      // Show/hide sold-out indicator
      const soldOutIndicator = this.container.querySelector('.swatch-sold-out-indicator');
      if (soldOutIndicator) {
        if (isSoldOut) {
          soldOutIndicator.hidden = false;
        } else {
          soldOutIndicator.hidden = true;
        }
      }

      // Show the variant visually
      this._previewFromEl(el);

      // Clear any revert timers since this is a permanent selection
      clearTimeout(this.hoverTimer);

      // Mark this card as having a selected variant
      this.container.dataset.selectedVariant = this.currentVariantId;

      // Also mirror it on the Quick Add button (belt-and-suspenders)
      const qaBtn = this.container.querySelector('[data-quick-add]');
      if (qaBtn) qaBtn.dataset.selectedVariant = this.currentVariantId;

      console.log('Card container updated:', this.container.dataset.selectedVariant);
    }

    _previewFromEl(el) {
      // Store the current variant ID for Quick Add
      this.currentVariantId = el.dataset.variantId;

      const variant = {
        src:    el.dataset.src    || el.dataset.imageUrl || '',
        srcset: el.dataset.srcset || '',
        sizes:  el.dataset.sizes  || ''
      };
      if (!variant.src && !variant.srcset) return;

      // Real change detection against the image actually being rendered
      const current = this.baseImg.currentSrc || this.baseImg.src || '';
      const normalize = (u) => u ? u.replace(/([?&])width=\d+/, '$1').replace(/[?&]$/, '') : u;
      if ((variant.src && normalize(current) === normalize(variant.src)) ||
          (variant.srcset && this.baseImg.srcset === variant.srcset)) {
        return;
      }

      // Update card link to point to this variant (progressive enhancement)
      if (this.cardLink && this.cardLink.dataset.linkWithVariant === 'true' && this.baseProductUrl) {
        this.cardLink.href = `${this.baseProductUrl}?variant=${el.dataset.variantId}`;
      }

      this._decodeThenCrossfade(variant);
    }

    _decodeThenCrossfade(variant) {
      const id = ++this.requestId;
      const probe = new Image();

      // Preload the VARIANT sources (your swatch already provides grid-aware sizing)
      if (variant.srcset) probe.srcset = variant.srcset;
      if (variant.sizes)  probe.sizes  = variant.sizes;
      probe.src = variant.src || this.baseImg.currentSrc || this.baseImg.src;

      const apply = () => {
        if (this.requestId !== id) return; // a newer hover won

        // Fill preview with VARIANT sources
        if (variant.srcset) this.previewImg.srcset = variant.srcset; else this.previewImg.removeAttribute('srcset');
        if (variant.sizes)  this.previewImg.sizes  = variant.sizes;  else this.previewImg.removeAttribute('sizes');
        if (variant.src)    this.previewImg.src    = variant.src;

        // Crossfade in; ensure preview has no stale intrinsic box (we rely on CSS sizing)
        this.previewImg.removeAttribute('width');
        this.previewImg.removeAttribute('height');
        this.previewImg.hidden = false;
        this.previewImg.style.opacity = '0';
        requestAnimationFrame(() => { this.previewImg.style.opacity = '1'; });

        // Do not commit variant to base during hover; only show preview layer.
        // The base image remains the original; pointerleave will hide preview.
        // This avoids any candidate mismatch "pop" from replacing base sources mid-hover.
        // Note: intentionally remove transitionend listener and commit timer.
      };

      if (probe.decode) {
        probe.decode().then(apply).catch(() => { probe.onload = apply; });
      } else {
        probe.onload = apply;
      }
    }

    _revertToOriginal() {
      // Clear the current variant selection
      this.currentVariantId = null;

      // Simply hide the preview layer and clear its sources
      if (!this.previewImg) return;

      const id = ++this.requestId;

      // Revert card link to base product URL
      if (this.cardLink && this.cardLink.dataset.linkWithVariant === 'true' && this.baseProductUrl) {
        this.cardLink.href = this.baseProductUrl;
      }

      // Fade out preview
      this.previewImg.style.opacity = '0';

      // After fade completes, hide and clear all sources for guaranteed empty state
      setTimeout(() => {
        if (this.requestId === id) {
          this.previewImg.hidden = true;
          // Clear sources to ensure empty state for next hover
          this.previewImg.removeAttribute('srcset');
          this.previewImg.removeAttribute('sizes');
          this.previewImg.removeAttribute('src');
          this.previewImg.removeAttribute('width');
          this.previewImg.removeAttribute('height');
        }
      }, 180);
    }
  }

  function initAll() {
    document.querySelectorAll('[data-enable-hover-preview="true"], .product-card[data-enable-hover-preview="true"]').forEach((el) => {
      new CardHoverPreview(el);
    });
  }

  document.addEventListener('DOMContentLoaded', initAll);
  document.addEventListener('shopify:section:load', (e) => {
    e.target?.querySelectorAll('[data-enable-hover-preview="true"], .product-card[data-enable-hover-preview="true"]').forEach((el) => {
      new CardHoverPreview(el);
    });
  });
})();
