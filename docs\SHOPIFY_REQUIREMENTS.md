# Shopify Theme Development Requirements & Common Errors

## Schema JSON Requirements

### Section Schema (`{% schema %}`)

#### URL Settings
- **Error**: `Invalid schema: setting with id="link" default must be a string or datasource access path`
- **Rule**: URL type settings cannot have `"#"` as default value
- **Solutions**:
  - Remove `default` property entirely: `"type": "url", "id": "link", "label": "Link"`
  - Use empty string: `"default": ""`
  - Use valid URL: `"default": "https://example.com"`

#### Range Settings  
- **Error**: `Range settings must have at most 101 steps`
- **Rule**: `(max - min) / step <= 101`
- **Example**: `min: 50, max: 400, step: 1` = 351 steps (INVALID)
- **Fix**: `min: 50, max: 400, step: 5` = 71 steps (VALID)

### Locale JSON Files (`locales/*.json`)

#### JSON Syntax
- **Error**: `Must be a valid JSON object`
- **Common Issues**:
  - Trailing commas: `"key": "value",}` (INVALID)
  - Duplicate keys: Multiple `"labels": {}` objects
  - Missing commas between properties
  - Unclosed brackets/braces

#### Required Locale Files
- Theme editor looks for translations in store locale (e.g., `en`, `en-CA`)
- Must create locale files matching store locales:
  - `en.default.json` (fallback)
  - `en.json` (for English stores)
  - `en-CA.json` (for Canadian English stores)

## Liquid Syntax Requirements

### Asset Tags Nesting
- **Error**: `'stylesheet' tag must not be nested inside other tags`
- **Rule**: `{% stylesheet %}` and `{% javascript %}` must be at top level
- **Invalid**:
  ```liquid
  {% if condition %}
    {% stylesheet %}
      /* CSS */
    {% endstylesheet %}
  {% endif %}
  ```
- **Valid**:
  ```liquid
  {% stylesheet %}
    /* CSS */
  {% endstylesheet %}
  
  {% if condition %}
    <!-- HTML content -->
  {% endif %}
  ```

### Font Picker Settings
- **Requirement**: Font picker defaults must use valid font IDs from Shopify's font system
- **Common IDs**: `work_sans_n4`, `jost_n4`, `helvetica_n4`
- **Invalid**: Custom font names or non-existent IDs

## Translation Key Patterns

### Schema Translations (`locales/*.schema.json`)
- Use consistent namespaces:
  - `t:labels.*` for setting labels
  - `t:options.*` for option values  
  - `t:general.*` for section names
- Example structure:
  ```json
  {
    "labels": {
      "logo": "Logo",
      "background": "Background"
    },
    "options": {
      "alignment": {
        "left": "Left",
        "center": "Center"
      }
    }
  }
  ```

### Theme Translations (`locales/*.json`)
- Use for user-facing content (not schema)
- Structure by feature/page:
  ```json
  {
    "cart": {
      "title": "Cart",
      "checkout": "Checkout"
    }
  }
  ```

## Best Practices to Avoid Errors

1. **Always validate JSON** before committing
2. **Test schema changes** in small increments
3. **Use consistent translation key patterns**
4. **Keep asset tags at file top-level**
5. **Verify font IDs** exist in Shopify's system
6. **Create locale files** for all store locales
7. **Remove invalid default values** (like `"#"` for URLs)

## Debugging Tips

1. **CLI Error Messages**: Pay attention to line numbers in error messages
2. **Schema Validation**: Test individual settings before adding multiple
3. **JSON Linting**: Use online JSON validators for complex structures
4. **Incremental Development**: Add one setting at a time to isolate issues
