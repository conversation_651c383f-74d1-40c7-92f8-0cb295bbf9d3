{%- comment -%}
  Enhanced Collection Section with Color Swatch Cards
  
  Purpose: Product grid with optional color swatches and hover preview
  - Card hover preview swaps main image to variant media
  - Click behavior: update product links or trigger quick-add
  - Configurable swatch display and interaction settings
  - Responsive grid with accessibility support
  
  Features:
  - Color swatch hover preview on cards
  - Quick add functionality
  - Responsive product grid
  - Filter and sort integration ready
  - Performance optimized with lazy loading
{%- endcomment -%}

{% comment %}theme-check-disable ParserBlockingJavaScript{% endcomment %}
<script src="{{ 'variant-hover.js' | asset_url }}" defer></script>
{%- render 'color-swatch-mapping' -%}

<style>
  .collection { padding: 2rem 0; }
  .collection__header { margin-bottom: 3rem; text-align: center; }
  .collection__title { font-size: 2.5rem; margin: 0 0 1rem; }
  .collection__description { max-width: 600px; margin: 0 auto; line-height: 1.6; }
  
  .collection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }
  
  /* Dynamic grid for better single product display */
  .collection-grid:has(> :only-child) {
    grid-template-columns: minmax(300px, 500px);
    justify-content: center;
  }
  
  .collection-grid:has(> :nth-child(2):last-child) {
    grid-template-columns: repeat(2, minmax(280px, 400px));
    justify-content: center;
  }
  
  .collection-grid:has(> :nth-child(3):last-child) {
    grid-template-columns: repeat(3, minmax(250px, 350px));
    justify-content: center;
  }
  
  /* Card swatch styles */
  .card__swatches {
    display: flex;
    gap: 0.25rem;
    margin: 0.75rem 0;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .swatch-wrapper {
    position: relative;
  }
  
  .swatch-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
  }
  
  .swatch {
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .swatch:hover,
  .swatch:focus {
    transform: scale(1.2);
    border-color: currentColor;
  }
  
  .swatch-input:checked + .swatch {
    border-color: currentColor;
    box-shadow: 0 0 0 1px white, 0 0 0 3px currentColor;
  }
  
  .swatch-more {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: rgba(var(--color-foreground), 0.7);
  }
  
  /* Responsive adjustments */
  @media (max-width: 749px) {
    .collection-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }
    
    .collection__title {
      font-size: 2rem;
    }
  }
  
  @media (max-width: 479px) {
    .collection-grid {
      grid-template-columns: 1fr;
    }
  }
</style>

<section class="collection section" data-section="{{ section.id }}">
  <div class="page-width">
    {%- if collection.title != blank or collection.description != blank -%}
      <div class="collection__header">
        {%- if collection.title != blank -%}
          <h1 class="collection__title">{{ collection.title | escape }}</h1>
        {%- endif -%}
        {%- if collection.description != blank -%}
          <div class="collection__description">
            {{ collection.description }}
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}

    {%- paginate collection.products by section.settings.products_per_page -%}
      {%- if collection.products.size > 0 -%}
        <div class="collection-grid">
          {%- assign product_count = collection.products.size -%}
          {%- assign grid_columns = 4 -%}
          
          {%- comment -%} Dynamic column calculation based on product count {%- endcomment -%}
          {%- if product_count == 1 -%}
            {%- assign grid_columns = 1 -%}
          {%- elsif product_count == 2 -%}
            {%- assign grid_columns = 2 -%}
          {%- elsif product_count == 3 -%}
            {%- assign grid_columns = 3 -%}
          {%- elsif product_count >= 4 -%}
            {%- assign grid_columns = 4 -%}
          {%- endif -%}
          
          {%- for product in collection.products -%}
            {%- render 'card-product',
              product: product,
              show_swatches: section.settings.show_swatches_on_cards,
              enable_hover_preview: section.settings.enable_card_hover_preview,
              max_swatches: section.settings.max_swatches_per_product,
              card_click_behavior: section.settings.card_click_behavior,
              show_quick_add: section.settings.enable_quick_add,
              card_image_aspect_ratio: section.settings.card_image_aspect_ratio,
              grid_position: forloop.index0,
              columns: grid_columns
            -%}
          {%- endfor -%}
        </div>

        {%- if paginate.pages > 1 -%}
          <div class="pagination-wrapper">
            {%- render 'pagination', paginate: paginate -%}
          </div>
        {%- endif -%}
      {%- else -%}
        <div class="collection-empty">
          <h2>{{ 'collections.general.no_matches' | t }}</h2>
          <p>{{ 'collections.general.no_products_html' | t }}</p>
        </div>
      {%- endif -%}
    {%- endpaginate -%}
  </div>
</section>

{% schema %}
{
  "name": "Collection",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Collection display"
    },
    {
      "type": "range",
      "id": "products_per_page",
      "min": 8,
      "max": 48,
      "step": 4,
      "default": 20,
      "label": "Products per page"
    },
    {
      "type": "header",
      "content": "Color swatches on cards"
    },
    {
      "type": "checkbox",
      "id": "show_swatches_on_cards",
      "label": "Show color swatches on product cards",
      "default": true,
      "info": "Display color options as swatches on collection cards"
    },
    {
      "type": "checkbox",
      "id": "enable_card_hover_preview",
      "label": "Enable card hover preview",
      "default": true,
      "info": "Hover over color swatches to preview variant images"
    },
    {
      "type": "range",
      "id": "max_swatches_per_product",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 5,
      "label": "Maximum swatches per product",
      "info": "Limit number of color swatches shown on each card"
    },
    {
      "type": "select",
      "id": "card_click_behavior",
      "label": "Swatch click behavior",
      "default": "link_with_variant",
      "options": [
        {
          "value": "link_with_variant",
          "label": "Go to product with variant selected"
        },
        {
          "value": "quick_add_variant",
          "label": "Add to cart via quick add"
        }
      ],
      "info": "How swatch clicks behave on collection cards"
    },
    {
      "type": "header",
      "content": "Card appearance"
    },
    {
      "type": "select",
      "id": "card_image_aspect_ratio",
      "label": "Card image aspect ratio",
      "default": "3/4",
      "options": [
        {
          "value": "3/4",
          "label": "Standard portrait (3:4)"
        },
        {
          "value": "4/5",
          "label": "Tall portrait (4:5)"
        },
        {
          "value": "1/1",
          "label": "Square (1:1)"
        },
        {
          "value": "5/4",
          "label": "Landscape (5:4)"
        },
        {
          "value": "16/9",
          "label": "Widescreen (16:9)"
        }
      ],
      "info": "Controls how product images are cropped. Portrait ratios work best for clothing."
    },
    {
      "type": "header",
      "content": "Quick add"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_add",
      "label": "Enable quick add to cart",
      "default": false,
      "info": "Show quick add buttons on product cards"
    }
  ]
}
{% endschema %}
