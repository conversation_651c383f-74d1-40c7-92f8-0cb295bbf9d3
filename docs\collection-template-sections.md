# Collection Template Sections

This document describes the new collection template sections created for the Ave theme. These sections provide a comprehensive, editor-driven collection page experience.

## Template Structure

The `templates/collection.json` now includes:

1. **Banner** (`callout-banner`) - Hero section
2. **Product Grid** (`collection-product-grid`) - Main product listing with filters
3. **Editorial Split 1** (`editorial-split`) - Content + product slider
4. **Featured Collection Slider** (`featured-collection-slider`) - Related products carousel
5. **Editorial Split 2** (`editorial-split`) - Content + product slider (flipped)
6. **Collections Grid** (`collection-list-grid`) - Other collections

## Sections Overview

### 1. Collection Product Grid (`collection-product-grid.liquid`)

**Purpose**: Main product listing with Shopify's native filtering and sorting.

**Features**:
- Uses Shopify storefront filters (Search & Discovery app)
- Pagination support (8-50 products per page)
- Promo blocks insertable at any product position
- Responsive grid (4-col desktop, 3-col tablet, 2-col mobile)
- Product cards with image ratios, vendor, ratings, quick add

**Promo Blocks**:
- Insert promotional content after Nth product
- Support for image or rich text content
- Configurable insertion position (1-50)

**Settings**:
- `page_size`: Products per page (8-50, step 4)
- `image_ratio`: Product image aspect ratio (adapt/portrait/square)
- `show_vendor`: Toggle vendor display
- `show_quick_add`: Enable quick add functionality
- `enable_filtering`: Toggle filter controls
- `enable_sorting`: Toggle sort dropdown

### 2. Editorial Split (`editorial-split.liquid`)

**Purpose**: Two-column layout with media and content + product slider.

**Features**:
- Media column: Image or video support
- Content column: Kicker, heading, description, CTA button
- Product slider: Individual products or collection-based
- Flip layout option (media right, content left)
- Accessible keyboard navigation (Arrow keys, Home/End)
- Touch/swipe support

**Product Blocks**:
- `product`: Single product picker
- `collection`: Collection + product limit (1-12)

**Settings**:
- `kicker`: Small text above heading
- `heading`: Main heading (rich text)
- `subheading`: Description (rich text)
- `button_label`/`button_link`: CTA button
- `media_type`: Image or video
- `image`/`video`: Media assets
- `flip_layout`: Boolean to reverse column order

**JavaScript**: `section-editorial-split.js`
- Responsive slide calculation
- Keyboard navigation (Arrow keys, Home/End)
- Touch/swipe gestures
- Screen reader announcements
- Intersection observer for performance

### 3. Featured Collection Slider (`featured-collection-slider.liquid`)

**Purpose**: Horizontal scrolling product carousel with autoplay support.

**Features**:
- Collection-based product display
- Configurable visible items (2-6) and rows (1-2)
- Optional autoplay with pause/play controls
- Dot indicators for navigation
- Accessibility-compliant carousel
- Responsive breakpoints

**Settings**:
- `collection`: Collection picker
- `heading`/`description`: Override collection title/description
- `show_view_all`: Toggle "View all" button
- `visible_items`: Products visible at once (2-6)
- `rows`: Number of rows (1-2)
- `autoplay`: Enable auto-rotation
- `autoplay_speed`: Rotation speed (3-10 seconds)
- Product card settings (image ratio, vendor, rating, quick add)

**JavaScript**: `section-featured-collection-slider.js`
- Grid-based layout system
- Autoplay with intersection observer
- Keyboard navigation and focus management
- Touch/swipe support
- Responsive visible items calculation
- Pause on hover/focus

### 4. Collection List Grid (`collection-list-grid.liquid`)

**Purpose**: Grid of collection cards, manual or automatic selection.

**Features**:
- Manual collection selection via blocks
- Automatic mode: top collections by product count
- Excludes current collection in auto mode
- Responsive grid (2-4 columns)
- Collection cards with images, titles, descriptions, product counts

**Selection Modes**:
- `manual`: Choose specific collections via blocks
- `automatic`: Auto-select by product count, excluding current

**Settings**:
- `selection_mode`: Manual or automatic
- `collections_count`: Number for automatic mode (2-12)
- `columns_desktop`: Desktop columns (2-4)
- `image_ratio`: Collection image aspect ratio
- `show_description`: Toggle collection descriptions
- `show_view_all`: Toggle "View all" button

## Accessibility Features

### Keyboard Navigation
- **Arrow Keys**: Navigate slides
- **Home/End**: Jump to first/last slide
- **Tab**: Focus navigation controls
- **Enter/Space**: Activate buttons

### Screen Reader Support
- Proper ARIA labels and roles
- Live region announcements for slide changes
- Semantic HTML structure
- Focus management

### Performance Optimizations
- Intersection Observer for lazy initialization
- Responsive image loading with appropriate sizes
- CSS containment for smooth animations
- Deferred JavaScript loading

## Browser Support

- Modern browsers (Chrome 60+, Firefox 60+, Safari 12+, Edge 79+)
- Progressive enhancement for older browsers
- No external dependencies
- Graceful degradation without JavaScript

## Theme Store Compliance

- All sections are editor-configurable
- No hardcoded IDs or handles
- Proper translation support
- Mobile-responsive design
- Accessibility standards compliance
- Performance optimized (Lighthouse goals: A11y ≥ 90, Performance ≥ 60)

## Installation Notes

1. **Assets**: Include JS files `section-editorial-split.js` and `section-featured-collection-slider.js`
2. **Utilities**: Include `collection-grid-utilities.css` for shared styles
3. **Snippets**: Ensure `card-collection.liquid` and `icon-close-small.liquid` exist
4. **Locales**: Update translation files with new strings
5. **Template**: Replace existing `collection.json` with new structure

## QA Checklist

- [ ] Promo blocks insert at correct product positions without breaking pagination
- [ ] Editorial split can flip media/content orientation
- [ ] Editorial split supports both image and video media
- [ ] Sliders operate with mouse, keyboard, and touch
- [ ] Slide changes are announced to screen readers
- [ ] Collection list works in both manual and automatic modes
- [ ] Automatic mode excludes current collection
- [ ] Template works on `/collections/all` and specific collections
- [ ] No console errors or layout shifts
- [ ] All text is translatable
- [ ] Responsive behavior across breakpoints

## Customization

Sections are designed to be highly customizable through the theme editor:

1. **Colors**: Each section supports color schemes
2. **Layout**: Grid columns, image ratios, flip layouts
3. **Content**: All text is editable, rich text support
4. **Products**: Flexible product selection (individual/collection-based)
5. **Behavior**: Autoplay, pagination, filtering options

The modular design allows merchants to create unique collection page layouts while maintaining consistency and performance.
