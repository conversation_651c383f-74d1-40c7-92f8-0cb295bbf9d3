{%- comment -%}
  Editorial Split Section
  
  Purpose: Two-column layout with media and content featuring product slider
  - Media can be image or video
  - Content includes text and product carousel
  - Accessible slider with keyboard navigation
  - Responsive layout with flip option
{%- endcomment -%}

<script src="{{ 'section-editorial-split.js' | asset_url }}" defer></script>

{% liquid
  assign color_scheme = section.settings.color_scheme | default: 'scheme-1'
  assign media_type = section.settings.media_type | default: 'image'
  assign flip_layout = section.settings.flip_layout | default: false
  assign section_id = 'EditorialSplit-' | append: section.id
%}

<section
  id="{{ section_id }}"
  class="editorial-split color-{{ color_scheme }} section-{{ section.id }}-padding{% if flip_layout %} editorial-split--flipped{% endif %}"
  data-section="{{ section.id }}"
  data-editorial-split
>
  <div class="editorial-split__inner page-width">
    <div class="editorial-split__container">
      
      {% comment %} Media Column {% endcomment %}
      <div class="editorial-split__media">
        {% if media_type == 'video' and section.settings.video != blank %}
          <div class="editorial-split__video-wrapper">
            {{
              section.settings.video
              | video_tag:
                controls: section.settings.enable_video_controls,
                autoplay: section.settings.enable_video_autoplay,
                loop: section.settings.enable_video_loop,
                muted: section.settings.enable_video_muted,
                class: 'editorial-split__video'
            }}
          </div>
        {% elsif section.settings.image != blank %}
          <div class="editorial-split__image-wrapper">
            {{
              section.settings.image
              | image_url: width: 800
              | image_tag:
                loading: 'lazy',
                class: 'editorial-split__image',
                widths: '400, 600, 800, 1000, 1200',
                sizes: '(min-width: 990px) 50vw, 100vw',
                alt: section.settings.image.alt | escape
            }}
          </div>
        {% else %}
          <div class="editorial-split__placeholder">
            {{ 'lifestyle-1' | placeholder_svg_tag: 'editorial-split__placeholder-svg' }}
          </div>
        {% endif %}
      </div>

      {% comment %} Content Column {% endcomment %}
      <div class="editorial-split__content">
        <div class="editorial-split__text-content">
          {% if section.settings.kicker != blank %}
            <p class="editorial-split__kicker">{{ section.settings.kicker }}</p>
          {% endif %}
          
          {% if section.settings.heading != blank %}
            <h2 class="editorial-split__heading h2">{{ section.settings.heading }}</h2>
          {% endif %}
          
          {% if section.settings.subheading != blank %}
            <div class="editorial-split__subheading rte">{{ section.settings.subheading }}</div>
          {% endif %}
          
          {% if section.settings.button_label != blank and section.settings.button_link != blank %}
            <a href="{{ section.settings.button_link }}" class="editorial-split__button button button--primary">
              {{ section.settings.button_label }}
            </a>
          {% endif %}
        </div>

        {% comment %} Product Slider {% endcomment %}
        {% if section.blocks.size > 0 %}
          <div 
            class="editorial-split__slider"
            role="region"
            aria-roledescription="carousel"
            aria-label="Featured products"
            data-slider
          >
            <div class="editorial-split__slider-viewport">
              <div 
                class="editorial-split__slider-track"
                tabindex="0"
                role="group"
                aria-live="polite"
                aria-atomic="false"
                data-slider-track
              >
                {% for block in section.blocks %}
                  <div 
                    class="editorial-split__slide"
                    role="group"
                    aria-roledescription="slide"
                    aria-label="Slide {{ forloop.index }} of {{ section.blocks.size }}"
                    data-slide-index="{{ forloop.index0 }}"
                    {{ block.shopify_attributes }}
                  >
                    {% case block.type %}
                      {% when 'product' %}
                        {% if block.settings.product != blank %}
                          {% render 'card-product',
                            product: block.settings.product,
                            image_ratio: 'square',
                            show_vendor: false,
                            show_secondary_image: false,
                            show_quick_add: true,
                            section_id: section.id
                          %}
                        {% endif %}
                      
                      {% when 'collection' %}
                        {% if block.settings.collection != blank %}
                          <div class="editorial-split__collection-slide">
                            {% if block.settings.collection_heading != blank %}
                              <h3 class="editorial-split__collection-title">{{ block.settings.collection_heading }}</h3>
                            {% endif %}
                            <div class="editorial-split__collection-grid">
                              {% assign products_limit = block.settings.products_to_show | default: 4 %}
                              {% for product in block.settings.collection.products limit: products_limit %}
                                {% render 'card-product',
                                  product: product,
                                  image_ratio: 'square',
                                  show_vendor: false,
                                  show_secondary_image: false,
                                  show_quick_add: false,
                                  section_id: section.id
                                %}
                              {% endfor %}
                            </div>
                            {% if block.settings.collection_button_label != blank %}
                              <a href="{{ block.settings.collection.url }}" class="editorial-split__collection-button button button--secondary">
                                {{ block.settings.collection_button_label }}
                              </a>
                            {% endif %}
                          </div>
                        {% endif %}
                    {% endcase %}
                  </div>
                {% endfor %}
              </div>
            </div>

            {% comment %} Slider Controls {% endcomment %}
            {% if section.blocks.size > 1 %}
              <div class="editorial-split__slider-controls">
                <button 
                  class="editorial-split__slider-button editorial-split__slider-button--prev"
                  aria-controls="{{ section_id }}-slider-track"
                  aria-label="Previous slide"
                  data-slider-prev
                  disabled
                >
                  <span class="visually-hidden">Previous slide</span>
                  <svg aria-hidden="true" focusable="false" width="12" height="12" viewBox="0 0 12 12">
                    <path d="M8 10L4 6l4-4" stroke="currentColor" stroke-width="2" fill="none"/>
                  </svg>
                </button>
                
                <div class="editorial-split__slider-pagination" aria-live="polite" aria-atomic="true">
                  <span data-slide-counter>1</span> of {{ section.blocks.size }}
                </div>
                
                <button 
                  class="editorial-split__slider-button editorial-split__slider-button--next"
                  aria-controls="{{ section_id }}-slider-track"
                  aria-label="Next slide"
                  data-slider-next
                >
                  <span class="visually-hidden">Next slide</span>
                  <svg aria-hidden="true" focusable="false" width="12" height="12" viewBox="0 0 12 12">
                    <path d="M4 2l4 4-4 4" stroke="currentColor" stroke-width="2" fill="none"/>
                  </svg>
                </button>
              </div>
            {% endif %}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</section>

{% stylesheet %}
  .editorial-split {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .editorial-split__inner {
    max-width: var(--page-width);
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .editorial-split__container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
    min-height: 60rem;
  }

  .editorial-split--flipped .editorial-split__container {
    direction: rtl;
  }

  .editorial-split--flipped .editorial-split__media,
  .editorial-split--flipped .editorial-split__content {
    direction: ltr;
  }

  .editorial-split__media {
    position: relative;
    height: 100%;
    min-height: 50rem;
  }

  .editorial-split__image-wrapper,
  .editorial-split__video-wrapper,
  .editorial-split__placeholder {
    width: 100%;
    height: 100%;
    border-radius: 1rem;
    overflow: hidden;
    background: rgba(var(--color-foreground), 0.04);
  }

  .editorial-split__image,
  .editorial-split__video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .editorial-split__placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .editorial-split__placeholder-svg {
    width: 50%;
    height: 50%;
    opacity: 0.3;
  }

  .editorial-split__content {
    display: flex;
    flex-direction: column;
    gap: 4rem;
    height: 100%;
    justify-content: center;
  }

  .editorial-split__text-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .editorial-split__kicker {
    font-size: 1.4rem;
    font-weight: 500;
    color: rgb(var(--color-accent-1));
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    margin: 0;
  }

  .editorial-split__heading {
    font-size: clamp(2.4rem, 4vw, 4.8rem);
    line-height: 1.1;
    margin: 0;
  }

  .editorial-split__subheading {
    font-size: 1.6rem;
    line-height: 1.6;
    color: rgba(var(--color-foreground), 0.8);
  }

  .editorial-split__button {
    align-self: flex-start;
    margin-top: 1rem;
  }

  .editorial-split__slider {
    border-top: 0.1rem solid rgba(var(--color-foreground), 0.1);
    padding-top: 3rem;
  }

  .editorial-split__slider-viewport {
    overflow: hidden;
    border-radius: 1rem;
  }

  .editorial-split__slider-track {
    display: flex;
    transition: transform 0.3s ease;
    outline: none;
  }

  .editorial-split__slider-track:focus {
    outline: 0.2rem solid rgb(var(--color-accent-1));
    outline-offset: 0.2rem;
  }

  .editorial-split__slide {
    flex: 0 0 100%;
    min-width: 0;
  }

  .editorial-split__collection-slide {
    text-align: center;
  }

  .editorial-split__collection-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 2rem;
  }

  .editorial-split__collection-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .editorial-split__collection-button {
    margin-top: 1rem;
  }

  .editorial-split__slider-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
  }

  .editorial-split__slider-button {
    width: 4rem;
    height: 4rem;
    border: 0.1rem solid rgba(var(--color-foreground), 0.2);
    border-radius: 50%;
    background: rgb(var(--color-background));
    color: rgb(var(--color-foreground));
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .editorial-split__slider-button:hover:not(:disabled) {
    border-color: rgb(var(--color-foreground));
    background: rgb(var(--color-foreground));
    color: rgb(var(--color-background));
  }

  .editorial-split__slider-button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .editorial-split__slider-pagination {
    font-size: 1.4rem;
    color: rgba(var(--color-foreground), 0.7);
  }

  @media screen and (max-width: 989px) {
    .editorial-split__container {
      grid-template-columns: 1fr;
      gap: 4rem;
      min-height: auto;
    }

    .editorial-split--flipped .editorial-split__container {
      direction: ltr;
    }

    .editorial-split__media {
      min-height: 40rem;
      order: -1;
    }

    .editorial-split__content {
      gap: 3rem;
    }

    .editorial-split__heading {
      font-size: clamp(2rem, 6vw, 3.6rem);
    }

    .editorial-split__collection-grid {
      grid-template-columns: 1fr;
    }
  }

  @media screen and (max-width: 749px) {
    .editorial-split {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }

    .editorial-split__container {
      gap: 3rem;
    }

    .editorial-split__media {
      min-height: 30rem;
    }

    .editorial-split__content {
      gap: 2rem;
    }

    .editorial-split__text-content {
      gap: 1.5rem;
    }

    .editorial-split__slider-controls {
      gap: 1rem;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Editorial split",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "kicker",
      "label": "Kicker text",
      "info": "Small text above the heading"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Editorial heading"
    },
    {
      "type": "richtext",
      "id": "subheading",
      "label": "Subheading",
      "default": "<p>Use this section to create compelling editorial content that showcases your brand story alongside featured products.</p>"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "select",
      "id": "media_type",
      "label": "Media type",
      "options": [
        {
          "value": "image",
          "label": "Image"
        },
        {
          "value": "video",
          "label": "Video"
        }
      ],
      "default": "image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "video",
      "id": "video",
      "label": "Video"
    },
    {
      "type": "checkbox",
      "id": "enable_video_controls",
      "default": true,
      "label": "Show video controls"
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplay",
      "default": false,
      "label": "Autoplay video"
    },
    {
      "type": "checkbox",
      "id": "enable_video_loop",
      "default": true,
      "label": "Loop video"
    },
    {
      "type": "checkbox",
      "id": "enable_video_muted",
      "default": true,
      "label": "Mute video"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "checkbox",
      "id": "flip_layout",
      "default": false,
      "label": "Flip layout",
      "info": "Show media on the right side instead of left"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    }
  ],
  "blocks": [
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        }
      ]
    },
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "text",
          "id": "collection_heading",
          "label": "Collection heading"
        },
        {
          "type": "range",
          "id": "products_to_show",
          "min": 2,
          "max": 8,
          "step": 1,
          "default": 4,
          "label": "Products to show"
        },
        {
          "type": "text",
          "id": "collection_button_label",
          "label": "Collection button label",
          "default": "View collection"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Editorial split"
    }
  ]
}
{% endschema %}
