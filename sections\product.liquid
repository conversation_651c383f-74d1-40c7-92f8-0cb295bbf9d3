{%- comment -%}
  Enhanced Product Section with Color Swatch Hover Preview
  
  Purpose: Full-fe  {%- comment -%} Product Info {%- endcomment -%}
  <div class="product__info-wrapper">
    <h1 class="product__title">{{ product.title | escape }}</h1>
    
    <div class="product__price">d product page with media gallery and variant selection
  - Color swatch hover preview changes featured media temporarily
  - Click selection updates variant, price, inventory, media, and URL
  - Accessible with keyboard navigation and screen reader support
  - Progressive enhancement - works without JavaScript
  
  Features:
  - Responsive media gallery with modal zoom
  - Color swatches with hover preview (configurable)
  - Variant selection with automatic updates
  - Smart inventory management
  - SEO-friendly variant URLs
{%- endcomment -%}

{% comment %}theme-check-disable ParserBlockingJavaScript{% endcomment %}
<script src="{{ 'variant-hover.js' | asset_url }}" defer></script>
<style>
  .product { margin-bottom: 3rem; }
  .product__media-wrapper { position: relative; margin-bottom: 2rem; }
  .product-media__featured { position: relative; }
  .product-media__featured img { width: 100%; height: auto; border-radius: 8px; }
  .product__info { max-width: 600px; }
  .product__title { margin: 0 0 1rem; font-size: 2rem; font-weight: bold; }
  .product__price { margin-bottom: 1rem; font-size: 1.5rem; font-weight: 600; }
  .product__description { margin-bottom: 2rem; line-height: 1.6; }
  
  /* Variant selectors */
  .product-form__input { margin-bottom: 1.5rem; }
  .product-form__input legend { font-weight: 600; margin-bottom: 0.5rem; display: block; }
  .variant-input-wrap { display: flex; flex-wrap: wrap; gap: 0.5rem; }
  
  /* Color swatches */
  .swatch-wrapper { position: relative; }
  .swatch-input { position: absolute; opacity: 0; pointer-events: none; }
  .swatch {
    display: inline-block;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
  }
  .swatch:hover, .swatch:focus { transform: scale(1.1); border-color: currentColor; }
  .swatch-input:checked + .swatch { border-color: currentColor; box-shadow: 0 0 0 2px white, 0 0 0 4px currentColor; }
  
  /* Size/other option styles */
  .variant-input { appearance: none; border: 1px solid #ccc; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; }
  .variant-input:checked { background: #000; color: white; }
  
  /* Form elements */
  .product-form__cart { display: flex; gap: 1rem; align-items: flex-end; margin-top: 2rem; }
  .quantity-input { width: 80px; padding: 0.75rem; border: 1px solid #ccc; border-radius: 4px; }
  .btn-product { padding: 1rem 2rem; background: #000; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 600; }
  .btn-product:hover { background: #333; }
  .btn-product:disabled { background: #ccc; cursor: not-allowed; }
  
  /* Responsive */
  @media (min-width: 750px) {
    .product { display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; align-items: start; }
    .product__media-wrapper { margin-bottom: 0; }
  }
</style>

<section 
  class="product section" 
  data-section="{{ section.id }}"
  data-enable-hover-preview="{{ section.settings.enable_color_hover_preview }}"
  data-revert-delay="{{ section.settings.revert_delay_ms }}"
  data-product-handle="{{ product.handle }}"
>
  {%- comment -%} Product Media {%- endcomment -%}
  <div class="product__media-wrapper">
    <div class="product-media__featured">
      {%- assign featured_media = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}
      {%- if featured_media -%}
        <div class="product-media" data-media-id="{{ featured_media.id }}" data-initial-visible>
          {{- featured_media
            | image_url: width: 2400
            | image_tag:
              widths: '750, 1100, 1500, 1800, 2100, 2400',
              sizes: '(min-width: 1200px) 50vw, 100vw',
              fetchpriority: 'high',
              loading: 'eager',
              decoding: 'async',
              alt: product.title,
              class: 'product-media__featured',
              width: featured_media.width,
              height: featured_media.height
          -}}
        </div>
      {%- endif -%}
      
      {%- comment -%} Hidden media for hover preview with smart loading {%- endcomment -%}
      {%- for media in product.media -%}
        {%- unless media.id == featured_media.id -%}
          {%- assign is_next_media = false -%}
          {%- if forloop.index <= 3 -%}
            {%- assign is_next_media = true -%}
          {%- endif -%}
          {%- assign priority_level = 'low' -%}
          {%- if is_next_media -%}
            {%- assign priority_level = 'high' -%}
          {%- endif -%}
          <div class="product-media" data-media-id="{{ media.id }}" style="display: none;" data-priority="{{ priority_level }}">
            {{- media
              | image_url: width: 2400
              | image_tag:
                widths: '750, 1100, 1500, 1800, 2100, 2400',
                sizes: '(min-width: 1200px) 50vw, 100vw',
                loading: 'lazy',
                decoding: 'async',
                alt: media.alt,
                class: 'product-media__image',
                width: media.width,
                height: media.height
            -}}
          </div>
        {%- endunless -%}
      {%- endfor -%}
    </div>
    
    {%- comment -%} Product Media Gallery Thumbnails {%- endcomment -%}
    {%- if product.media.size > 1 -%}
      <div class="product-media__gallery">
        {%- for media in product.media -%}
          {%- assign loading_value = 'lazy' -%}
          {%- if forloop.first -%}
            {%- assign loading_value = 'eager' -%}
          {%- endif -%}
          <button
            type="button"
            class="product-media__thumb{% if forloop.first %} active{% endif %}"
            data-media-id="{{ media.id }}"
            data-media-position="{{ forloop.index0 }}"
            aria-label="View image {{ forloop.index }} of {{ product.media.size }}"
          >
            {{- media
              | image_url: width: 200
              | image_tag:
                widths: '100, 150, 200',
                sizes: '100px',
                loading: loading_value,
                decoding: 'async',
                alt: media.alt,
                class: 'product-media__thumb-image',
                width: '100',
                height: '100'
            -}}
          </button>
        {%- endfor -%}
      </div>
    {%- endif -%}
  </div>

  {%- comment -%} Product Info {%- endcomment -%}
  <div class="product__info">
    <h1 class="product__title">{{ product.title | escape }}</h1>
    
    <div class="product__price">
      <span class="price">
        {{ product.selected_or_first_available_variant.price | money }}
      </span>
      {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
        <span class="price price--compare">
          {{ product.selected_or_first_available_variant.compare_at_price | money }}
        </span>
      {%- endif -%}
    </div>

    {%- if product.description != blank -%}
      <div class="product__description">
        {{ product.description }}
      </div>
    {%- endif -%}

    {%- comment -%} Product Form {%- endcomment -%}
    {% form 'product', product, class: 'product-form' %}
      <variant-selects data-section="{{ section.id }}" data-url="{{ product.url }}">
        <script type="application/json">
          [
            {%- for variant in product.variants -%}
              {
                "id": {{ variant.id }},
                "title": {{ variant.title | json }},
                "option1": {{ variant.option1 | json }},
                "option2": {{ variant.option2 | json }},
                "option3": {{ variant.option3 | json }},
                "options": [
                  {%- if variant.option1 -%}{{ variant.option1 | json }}{%- endif -%}
                  {%- if variant.option2 -%},{{ variant.option2 | json }}{%- endif -%}
                  {%- if variant.option3 -%},{{ variant.option3 | json }}{%- endif -%}
                ],
                "price": {{ variant.price }},
                "compare_at_price": {{ variant.compare_at_price | default: 0 }},
                "available": {{ variant.available | json }},
                "inventory_quantity": {{ variant.inventory_quantity | default: 0 }},
                "inventory_management": {{ variant.inventory_management | json }},
                "inventory_policy": {{ variant.inventory_policy | json }},
                "featured_media": {
                  {%- if variant.featured_media -%}
                    "id": {{ variant.featured_media.id }},
                    "alt": {{ variant.featured_media.alt | json }},
                    "src": {{ variant.featured_media | image_url: width: 1000 | json }}
                  {%- else -%}
                    "id": null,
                    "alt": null,
                    "src": null
                  {%- endif -%}
                }
              }{%- unless forloop.last -%},{%- endunless -%}
            {%- endfor -%}
          ]
        </script>
        {%- for option in product.options_with_values -%}
          {%- liquid
            assign option_name = option.name | downcase
            assign is_color = false
            if option_name contains 'color' or option_name contains 'colour'
              assign is_color = true
            endif
          -%}
          
          <fieldset class="product-form__input">
            <legend>{{ option.name }}:</legend>
            <div class="variant-input-wrap{% if is_color %} variant-input-wrap--swatches{% endif %}">
              {%- for value in option.values -%}
                {%- liquid
                  assign variant_for_value = null
                  for variant in product.variants
                    case option.position
                      when 1
                        assign option_value = variant.option1
                      when 2
                        assign option_value = variant.option2
                      when 3
                        assign option_value = variant.option3
                    endcase
                    
                    if option_value == value
                      assign variant_for_value = variant
                      break
                    endif
                  endfor
                  
                  assign variant_media_id = variant_for_value.featured_media.id | default: product.featured_media.id
                -%}
                
                <div class="variant-option{% if is_color %} swatch-wrapper{% endif %}">
                  <input
                    type="radio"
                    name="options[{{ option.name | escape }}]"
                    value="{{ value | escape }}"
                    id="option-{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                    class="{% if is_color %}swatch-input{% else %}variant-input{% endif %}"
                    {% if option.selected_value == value %}checked{% endif %}
                    {% if is_color and variant_for_value %}
                      data-variant-id="{{ variant_for_value.id }}"
                      data-media-id="{{ variant_media_id }}"
                      data-color-name="{{ value | escape }}"
                    {% endif %}
                  >
                  <label 
                    for="option-{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                    class="{% if is_color %}swatch swatch--{{ value | handle }}{% else %}variant-label{% endif %}"
                    {% if is_color %}
                      data-variant-id="{{ variant_for_value.id }}"
                      data-media-id="{{ variant_media_id }}"
                      data-color-name="{{ value | escape }}"
                    {% endif %}
                  >
                    {% if is_color %}
                      <span class="visually-hidden">{{ value | escape }}</span>
                    {% else %}
                      {{ value | escape }}
                    {% endif %}
                  </label>
                </div>
              {%- endfor -%}
            </div>
          </fieldset>
        {%- endfor -%}

        <script type="application/json" data-selected-variant>
          {{ product.selected_or_first_available_variant | json }}
        </script>
      </variant-selects>

      {%- comment -%} Quantity and Add to Cart {%- endcomment -%}
      <div class="product-form__cart">
        <div class="product-form__quantity">
          <label for="quantity-{{ section.id }}">Quantity:</label>
          <input 
            type="number" 
            name="quantity" 
            id="quantity-{{ section.id }}"
            min="1" 
            value="1"
            class="quantity-input"
          >
        </div>
        
        <button
          type="submit"
          name="add"
          class="btn-product"
          {% unless product.selected_or_first_available_variant.available %}disabled{% endunless %}
        >
          <span class="btn-text">
            {%- if product.selected_or_first_available_variant.available -%}
              Add to cart
            {%- else -%}
              Sold out
            {%- endif -%}
          </span>
        </button>
      </div>

      {%- comment -%} Hidden variant selector for form submission {%- endcomment -%}
      <input 
        type="hidden" 
        name="id" 
        value="{{ product.selected_or_first_available_variant.id }}"
        disabled
      >
    {% endform %}
  </div>
</section>

{%- comment -%} Variant Selection Script {%- endcomment -%}
{% comment %}theme-check-disable ParserBlockingJavaScript{% endcomment %}
<script src="{{ 'variant-hover.js' | asset_url }}" defer></script>
{%- render 'color-swatch-mapping' -%}

{% comment %}theme-check-disable RemoteAsset{% endcomment %}
<style>
  /* Performance Optimizations */
  .product__media-wrapper {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
  }
  
  @media (min-width: 990px) {
    .product__media-wrapper {
      grid-template-columns: 1fr 100px;
      grid-template-areas: "featured gallery";
    }
  }
  
  .product-media__featured {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: 8px;
    grid-area: featured;
  }
  
  .product-media__featured img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: opacity 0.3s ease;
  }
  
  .product-media__gallery {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    grid-area: gallery;
  }
  
  @media (min-width: 990px) {
    .product-media__gallery {
      flex-direction: column;
      overflow-y: auto;
      overflow-x: visible;
      max-height: 500px;
    }
  }
  
  .product-media__thumb {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border: 2px solid transparent;
    border-radius: 6px;
    overflow: hidden;
    background: none;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0;
  }
  
  .product-media__thumb:hover,
  .product-media__thumb.active {
    border-color: rgb(var(--color-foreground));
    transform: scale(1.05);
  }
  
  .product-media__thumb-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
  
  .product-media[style*="none"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    pointer-events: none;
  }
  
  .product-media:not([style*="none"]) {
    opacity: 1;
    pointer-events: auto;
  }
  
  /* Smooth transitions for media switching */
  .product-media__featured .product-media {
    transition: opacity 0.3s ease;
  }
  
  /* Blur-up loading placeholders */
  .product-media__image.loading {
    filter: blur(8px);
    transform: scale(1.02);
    transition: filter 0.3s ease, transform 0.3s ease;
  }
  
  .product-media__image.loaded {
    filter: none;
    transform: scale(1);
  }
  
  /* Progressive image enhancement */
  .product-media__placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity 0.3s ease;
  }
  
  .product-media__placeholder.hidden {
    opacity: 0;
    pointer-events: none;
  }
  
  /* Product Page Swatch Styling */
  .variant-input-wrap--swatches {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 0.5rem;
  }
  
  .swatch-wrapper {
    position: relative;
  }
  
  .swatch {
    display: block;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
  }
  
  .swatch:hover,
  .swatch:focus {
    transform: scale(1.15);
    box-shadow: 0 0 0 2px rgb(var(--color-background)), 0 0 0 4px rgb(var(--color-foreground));
  }
  
  .swatch-input:checked + .swatch {
    transform: scale(1.15);
    box-shadow: 0 0 0 2px rgb(var(--color-background)), 0 0 0 4px rgb(var(--color-foreground));
  }
  
  .swatch-input {
    position: absolute !important;
    overflow: hidden;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    clip: rect(0 0 0 0);
    word-wrap: normal !important;
  }
  
  /* Color-specific swatch styles */
  .swatch--green,
  .swatch--forest-green,
  .swatch--olive { background-color: #4a5d3a; }
  
  .swatch--pink,
  .swatch--light-pink,
  .swatch--rose { background-color: #f8bbd9; }
  
  .swatch--black { background-color: #000000; }
  .swatch--white { background-color: #ffffff; border: 2px solid #e0e0e0; }
  .swatch--red { background-color: #dc2626; }
  .swatch--blue { background-color: #2563eb; }
  .swatch--yellow { background-color: #facc15; }
  .swatch--purple { background-color: #9333ea; }
  .swatch--orange { background-color: #ea580c; }
  .swatch--brown { background-color: #92400e; }
  .swatch--gray,
  .swatch--grey { background-color: #6b7280; }
  .swatch--navy { background-color: #1e3a8a; }
  .swatch--beige { background-color: #f5f5dc; border: 1px solid #d0d0d0; }
  .swatch--cream { background-color: #fffdd0; border: 1px solid #d0d0d0; }
  
  /* Fallback for unmapped colors */
  .swatch:not([class*="swatch--"]) {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 6px 6px;
    background-position: 0 0, 0 3px, 3px -3px, -3px 0px;
    border: 2px solid #ccc;
  }
  
  .variant-label {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: 1px solid rgb(var(--color-foreground));
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .variant-label:hover,
  .variant-input:checked + .variant-label {
    background-color: rgb(var(--color-foreground));
    color: rgb(var(--color-background));
  }
  
  /* Product Media Layout */
  .product-media {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
  }
  
  @media screen and (min-width: 750px) {
    .product-media {
      grid-template-columns: 100px 1fr;
      gap: 1.5rem;
    }
  }
  
  /* Gallery thumbnails */
  .product-media__list {
    display: grid;
    gap: 0.75rem;
    grid-auto-rows: 80px;
    overflow-y: auto;
    max-height: 400px;
    scrollbar-width: thin;
  }
  
  @media screen and (max-width: 749px) {
    .product-media__list {
      grid-auto-flow: column;
      grid-auto-columns: 80px;
      overflow-x: auto;
      overflow-y: visible;
      max-height: none;
    }
  }
  
  .product-media__thumb {
    aspect-ratio: 1;
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
  }
  
  .product-media__thumb.selected {
    border-color: rgb(var(--color-foreground));
  }
  
  .product-media__thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
  }
  
  /* Featured media container */
  .product-media__featured {
    position: relative;
    aspect-ratio: 1;
    border-radius: 12px;
    overflow: hidden;
    background: rgb(var(--color-background));
  }
  
  .product-media__featured img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
  }
  
  /* Blur-up loading effects */
  .product-media img {
    transition: opacity 0.3s ease, filter 0.3s ease;
  }
  
  .product-media img.loading {
    opacity: 0.8;
    filter: blur(8px);
  }
  
  .product-media img.loaded {
    opacity: 1;
    filter: none;
  }
  
  /* Prefetch hint for performance */
  .product-media__thumb[data-prefetched="true"] {
    position: relative;
  }
  
  .product-media__thumb[data-prefetched="true"]::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 4px;
    height: 4px;
    background: var(--gradient-accent);
    border-radius: 50%;
    opacity: 0.7;
  }
  
  /* Smooth scrolling for thumbnail navigation */
  .product-media__list {
    scroll-behavior: smooth;
  }
  
  /* Enhanced hover states */
  .product-media__thumb:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.15);
  }
  
  /* Focus states for accessibility */
  .product-media__thumb:focus-visible {
    outline: 2px solid rgb(var(--color-foreground));
    outline-offset: 2px;
  }
</style>

<script>
  class VariantSelects extends HTMLElement {
    constructor() {
      super();
      this.addEventListener('change', this.onVariantChange);
      this.initGallery();
      this.trackImagePerformance();
    }

    // Initialize product gallery
    initGallery() {
      this.galleryThumbs = document.querySelectorAll('.product-media__thumb');
      this.featuredContainer = document.querySelector('.product-media__featured');
      this.prefetchedImages = new Set();
      
      this.setupIntersectionObserver();
      
      this.galleryThumbs.forEach((thumb, index) => {
        // Prefetch next images on approach
        if (index <= 2) {
          this.prefetchThumbnailImage(thumb);
        }
        
        // Add click handlers
        thumb.addEventListener('click', (e) => this.handleThumbnailClick(e));
        
        // Add hover prefetching for remaining images
        thumb.addEventListener('mouseenter', (e) => {
          this.prefetchThumbnailImage(e.currentTarget);
        }, { passive: true });
      });
    }
    
    // Setup intersection observer for smart loading
    setupIntersectionObserver() {
      if (!('IntersectionObserver' in window)) return;
      
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            this.loadImageWithBlurUp(img);
            observer.unobserve(img);
          }
        });
      }, { rootMargin: '100px' });
      
      // Observe all lazy images
      document.querySelectorAll('img[loading="lazy"]').forEach(img => {
        observer.observe(img);
      });
    }
    
    // Load image with blur-up effect
    loadImageWithBlurUp(img) {
      if (img.complete) return;
      
      img.classList.add('loading');
      
      const handleLoad = () => {
        img.classList.remove('loading');
        img.classList.add('loaded');
        img.removeEventListener('load', handleLoad);
      };
      
      img.addEventListener('load', handleLoad);
    }
    
    // Prefetch thumbnail image
    prefetchThumbnailImage(thumb) {
      const mediaId = thumb.dataset.mediaId;
      const mediaElement = document.querySelector(`[data-media-id="${mediaId}"] img`);
      
      if (mediaElement && !this.prefetchedImages.has(mediaId)) {
        const imageUrl = mediaElement.src || mediaElement.dataset.src;
        if (imageUrl) {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'image';
          link.href = imageUrl;
          document.head.appendChild(link);
          this.prefetchedImages.add(mediaId);
        }
      }
    }
    
    // Handle thumbnail click
    handleThumbnailClick(event) {
      const clickedThumb = event.currentTarget;
      const mediaId = clickedThumb.dataset.mediaId;
      
      // Update active thumbnail
      this.galleryThumbs.forEach(thumb => thumb.classList.remove('active'));
      clickedThumb.classList.add('active');
      
      // Switch featured media
      this.switchFeaturedMedia(mediaId);
    }
    
    // Switch featured media with smooth transition
    switchFeaturedMedia(mediaId) {
      const currentMedia = this.featuredContainer.querySelector('.product-media:not([style*="none"])');
      const targetMedia = this.featuredContainer.querySelector(`[data-media-id="${mediaId}"]`);
      
      if (currentMedia && targetMedia && currentMedia !== targetMedia) {
        // Fade out current
        currentMedia.style.transition = 'opacity 0.2s ease';
        currentMedia.style.opacity = '0';
        
        setTimeout(() => {
          currentMedia.style.display = 'none';
          targetMedia.style.display = 'block';
          targetMedia.style.opacity = '0';
          
          // Fade in new
          requestAnimationFrame(() => {
            targetMedia.style.transition = 'opacity 0.2s ease';
            targetMedia.style.opacity = '1';
          });
        }, 200);
      }
    }

    onVariantChange() {
      this.updateOptions();
      this.updateMasterId();
      this.toggleAddButton(true, '', false);
      this.updatePickupAvailability();

      if (!this.currentVariant) {
        this.toggleAddButton(true, '', true);
        this.setUnavailable();
      } else {
        this.updateMedia();
        this.updateURL();
        this.updateVariantInput();
        this.renderProductInfo();
      }
    }

    updateOptions() {
      this.options = Array.from(this.querySelectorAll('input[type="radio"]:checked'), (input) => input.value);
    }

    updateMasterId() {
      const variantData = this.getVariantData();
      if (!Array.isArray(variantData)) {
        console.error('Variant data is not an array:', variantData);
        return;
      }
      
      this.currentVariant = variantData.find((variant) => {
        return !variant.options.map((option, index) => {
          return this.options[index] === option;
        }).includes(false);
      });
    }

    updateMedia() {
      if (!this.currentVariant || !this.currentVariant.featured_media) return;
      
      const mediaGallery = document.querySelector('.product-media__featured');
      if (!mediaGallery) return;

      const currentMedia = mediaGallery.querySelector('.product-media:not([style*="none"])');
      const targetMedia = mediaGallery.querySelector(`[data-media-id="${this.currentVariant.featured_media.id}"]`);
      
      if (targetMedia && currentMedia !== targetMedia) {
        currentMedia.style.display = 'none';
        targetMedia.style.display = 'block';
      }
    }

    updateURL() {
      if (!this.currentVariant || this.dataset.updateUrl === 'false') return;
      window.history.replaceState({}, '', `${this.dataset.url}?variant=${this.currentVariant.id}`);
    }

    updateVariantInput() {
      const productForm = document.querySelector('.product-form');
      const input = productForm.querySelector('input[name="id"]');
      input.value = this.currentVariant.id;
      input.dispatchEvent(new Event('change', { bubbles: true }));
    }

    renderProductInfo() {
      const price = document.querySelector('.product__price .price');
      const comparePrice = document.querySelector('.product__price .price--compare');
      
      if (price) {
        price.textContent = this.formatMoney(this.currentVariant.price);
      }
      
      if (comparePrice) {
        if (this.currentVariant.compare_at_price > this.currentVariant.price) {
          comparePrice.textContent = this.formatMoney(this.currentVariant.compare_at_price);
          comparePrice.style.display = 'inline';
        } else {
          comparePrice.style.display = 'none';
        }
      }
    }

    formatMoney(cents) {
      return (cents / 100).toLocaleString('en-US', {
        style: 'currency',
        currency: 'USD'
      });
    }

    toggleAddButton(disable = true, text, modifyClass = true) {
      const productForm = document.querySelector('.product-form');
      if (!productForm) return;

      const addButton = productForm.querySelector('[name="add"]');
      const addButtonText = productForm.querySelector('.btn-text');

      if (!addButton) return;

      if (disable) {
        addButton.setAttribute('disabled', 'disabled');
        if (text) addButtonText.textContent = text;
      } else {
        addButton.removeAttribute('disabled');
        addButtonText.textContent = text;
      }
    }

    setUnavailable() {
      const button = document.querySelector('.btn-product');
      const buttonText = document.querySelector('.btn-text');
      
      button.setAttribute('disabled', 'disabled');
      buttonText.textContent = 'Unavailable';
    }

    updatePickupAvailability() {
      // Placeholder for pickup availability update
    }

    getVariantData() {
      if (this.variantData) return this.variantData;
      
      try {
        const script = this.querySelector('[type="application/json"]');
        if (!script) {
          console.error('No variant data script found');
          return [];
        }
        
        this.variantData = JSON.parse(script.textContent);
        return Array.isArray(this.variantData) ? this.variantData : [];
      } catch (error) {
        console.error('Error parsing variant data:', error);
        return [];
      }
    }
    
    // Performance monitoring for "Prada-smooth" loading
    trackImagePerformance() {
      if (typeof PerformanceObserver === 'undefined' || !window.location.hostname.includes('localhost')) return;
      
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.name.includes('cdn.shopify.com') && entry.initiatorType === 'img') {
            const filename = entry.name.split('/').pop();
            const loadTime = entry.duration.toFixed(2);
            console.log(`🚀 Image Performance: ${filename} loaded in ${loadTime}ms`);
            
            // Track if we're hitting the sweet spot (under 100ms for cached, under 500ms for fresh)
            if (entry.duration < 100) {
              console.log(`✅ Prada-smooth: ${filename} (cached)`);
            } else if (entry.duration < 500) {
              console.log(`⚡ Good performance: ${filename}`);
            } else {
              console.log(`⚠️  Optimize: ${filename} took ${loadTime}ms`);
            }
          }
        });
      });
      
      observer.observe({ entryTypes: ['resource'] });
    }
  }

  customElements.define('variant-selects', VariantSelects);
</script>

{% schema %}
{
  "name": "Product",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Color swatch preview"
    },
    {
      "type": "checkbox",
      "id": "enable_color_hover_preview",
      "label": "Enable color hover preview",
      "default": true,
      "info": "Hover over color swatches to preview variant images"
    },
    {
      "type": "range",
      "id": "revert_delay_ms",
      "min": 0,
      "max": 1000,
      "step": 100,
      "unit": "ms",
      "label": "Preview revert delay",
      "default": 0,
      "info": "Delay before reverting hover preview (0 = instant)"
    },
    {
      "type": "header",
      "content": "Product media"
    },
    {
      "type": "checkbox",
      "id": "enable_zoom",
      "label": "Enable image zoom",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "label": "Hide sold out variants",
      "default": false
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
