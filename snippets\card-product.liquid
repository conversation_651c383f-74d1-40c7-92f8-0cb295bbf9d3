{%- comment -%}
  Enhanced Product Card with Color Swatch Support

  Purpose: Renders product cards with optional color variant swatches
  - Hover preview swaps card image to variant media
  - Click behavior configurable: update links or quick-add
  - Accessibility-first with radio inputs and proper labeling

  Usage: {% render 'card-product', product: product, show_swatches: true %}

  Parameters:
  - product: required product object
  - show_swatches: boolean, renders color swatches when true
  - enable_hover_preview: boolean, enables hover preview functionality
  - max_swatches: number, limits displayed swatches (default 5)
  - card_click_behavior: 'link_with_variant' or 'quick_add_variant'
  - show_quick_add: boolean, shows quick add button
{%- endcomment -%}

{%- liquid
  assign show_swatches = show_swatches | default: false
  assign enable_hover_preview = enable_hover_preview | default: false
  assign max_swatches = max_swatches | default: 5
  assign card_click_behavior = card_click_behavior | default: 'link_with_variant'
  assign show_quick_add = show_quick_add | default: false
  assign grid_position = grid_position | default: 999
  assign columns = columns | default: 4

  # Above-the-fold optimization
  assign is_above_fold = false
  if grid_position < columns
    assign is_above_fold = true
  endif

  assign color_option = null
  assign color_values = blank

  # Find color option
  for option in product.options_with_values
    assign option_name = option.name | downcase
    if option_name contains 'color' or option_name contains 'colour'
      assign color_option = option
      assign color_values = option.values
      break
    endif
  endfor

  # If no color option found and only one option exists, use it
  if color_option == null and product.options_with_values.size == 1
    assign color_option = product.options_with_values[0]
    assign color_values = color_option.values
  endif

  # Determine default media for the card (prefer merchant's variant order)
  assign card_media = product.featured_media

  if color_option and color_option.values.size > 0
    assign first_color = color_option.values | first
    assign chosen_variant = nil
    assign chosen_pos = 9999
    for v in product.variants
      assign v_color = v.option1
      if color_option.position == 2
        assign v_color = v.option2
      elsif color_option.position == 3
        assign v_color = v.option3
      endif
      if v.available and v_color == first_color and v.position < chosen_pos
        assign chosen_variant = v
        assign chosen_pos = v.position
      endif
    endfor
    if chosen_variant and chosen_variant.featured_media
      assign card_media = chosen_variant.featured_media
    endif
  else
    assign fav = product.selected_or_first_available_variant
    if fav and fav.featured_media
      assign card_media = fav.featured_media
    endif
  endif


  # Build variant-to-media mapping for swatches
  assign variant_media_map = blank
  if show_swatches and color_option != null
    assign variant_media_map = ''
    for variant in product.variants
      if variant.available
        assign variant_color = variant.option1
        if color_option.position == 2
          assign variant_color = variant.option2
        elsif color_option.position == 3
          assign variant_color = variant.option3
        endif

        # Find first media for this variant
        assign variant_media = null
        for media in variant.featured_media
          assign variant_media = media
          break
        endfor

        # Fallback to product featured media
        if variant_media == null
          assign variant_media = product.featured_media
        endif

        if variant_media != null
          assign map_entry = variant.id | append: ':' | append: variant_media.id | append: ':' | append: variant_color | append: ';'
          assign variant_media_map = variant_media_map | append: map_entry
        endif
      endif
    endfor
  endif
-%}

<div
  class="card-wrapper underline-links-hover"
  data-product-card
  data-product-handle="{{ product.handle }}"
  {%- if enable_hover_preview -%}data-enable-hover-preview="true"{%- endif -%}
  {%- if card_click_behavior -%}data-card-click-behavior="{{ card_click_behavior }}"{%- endif -%}
>
  <div class="card card--{{ settings.card_style }}{% unless product.available %} card--sold-out{% endunless %}{% if product.compare_at_price > product.price and product.available %} card--on-sale{% endif %}">

    {%- comment -%} Card Media Configuration {%- endcomment -%}
    {%- liquid
      # Set aspect ratio from section setting or sensible default
      assign card_aspect = card_image_aspect_ratio | default: '3/4'

      # Calculate responsive image sizing based on grid columns
      # Higher resolution needed for aspect ratio cropping
      case columns
        when 1, 2
          assign image_width = 1200
          assign widths_attr = '800, 1000, 1200, 1400'
          assign sizes_attr = '(min-width: 990px) 50vw, 100vw'
        when 3
          assign image_width = 1000
          assign widths_attr = '600, 800, 1000, 1200'
          assign sizes_attr = '(min-width: 990px) 33vw, 50vw'
        else
          assign image_width = 800
          assign widths_attr = '400, 600, 800, 1000'
          assign sizes_attr = '(min-width: 990px) calc(100vw / 4), 50vw'
      endcase

      # Better object position for fashion/clothing - show upper portion
      assign default_object_position = 'center top'
    -%}

    <div class="card__media"
         style="--card-aspect: {{ card_aspect }}; --card-object-position: {{ product.metafields.theme.card_object_position | default: default_object_position }};">
      {%- assign loading_value = 'lazy' -%}
      {%- assign fetchpriority_value = 'auto' -%}

      {%- if is_above_fold -%}
        {%- assign loading_value = 'eager' -%}
        {%- assign fetchpriority_value = 'high' -%}
      {%- endif -%}

      {%- comment -%} Card media (no theme hover effect) {%- endcomment -%}
      <div class="media media--transparent">
        {{- card_media
          | image_url: width: image_width
          | image_tag:
            widths: widths_attr,
            sizes: sizes_attr,
            loading: loading_value,
            fetchpriority: fetchpriority_value,
            decoding: 'async',
            alt: product.title,
            class: 'motion-reduce card-media__primary'
        -}}
        {%- comment -%} Preview layer used for swatch hover crossfades {%- endcomment -%}
        <img class="card-media__preview" alt="" aria-hidden="true" hidden width="1" height="1">
      </div>
    </div>

    {%- comment -%} Card Content {%- endcomment -%}
    <div class="card__content">
      <div class="card__information">
        <h3 class="card__heading">
          {%- assign base_card_url = product.url | within: collection -%}
          <a href="{{ base_card_url }}"
             id="StandardCardNoMediaLink-{{ product.id }}"
             class="full-unstyled-link card-link"
             data-link-with-variant="true"
             aria-labelledby="StandardCardNoMediaLink-{{ product.id }} NoMediaStandardBadge-{{ product.id }}">
            {{ product.title | escape }}
          </a>
        </h3>
      </div>

      {%- comment -%} Color Swatches {%- endcomment -%}
      {%- if show_swatches and color_option != null -%}
        <div class="card__swatches" role="group" aria-label="Color options">
          {%- assign swatch_count = 0 -%}
          {%- assign displayed_colors = blank -%}

          {%- for color_value in color_values -%}
            {%- unless displayed_colors contains color_value -%}
              {%- assign displayed_colors = displayed_colors | append: color_value | append: ',' -%}
              {%- assign swatch_count = swatch_count | plus: 1 -%}

              {%- if swatch_count <= max_swatches -%}
                {%- comment -%} Find variant for this color {%- endcomment -%}
                {%- assign color_variant = null -%}
                {%- for variant in product.variants -%}
                  {%- assign variant_color = variant.option1 -%}
                  {%- if color_option.position == 2 -%}
                    {%- assign variant_color = variant.option2 -%}
                  {%- elsif color_option.position == 3 -%}
                    {%- assign variant_color = variant.option3 -%}
                  {%- endif -%}

                  {%- if variant_color == color_value -%}
                    {%- assign color_variant = variant -%}
                  {%- endif -%}
                {%- endfor -%}

                {%- if color_variant -%}
                  {%- comment -%} Build responsive sources for this variant {%- endcomment -%}
                  {%- assign media_for_color = color_variant.featured_media | default: card_media -%}
                  {%- assign widths_list = widths_attr | replace: ' ', '' | split: ',' -%}
                  {%- capture variant_srcset -%}
                    {%- for w in widths_list -%}
                      {{ media_for_color | image_url: width: w }} {{ w }}w{% unless forloop.last %}, {% endunless %}
                    {%- endfor -%}
                  {%- endcapture -%}

                  <button
                    type="button"
                    class="swatch swatch--{{ color_value | handle }}{% unless color_variant.available %} swatch--sold-out{% endunless %}"
                    aria-label="{{ color_value | escape }}{% unless color_variant.available %} ({{ 'products.product.sold_out' | t }}){% endunless %}"
                    title="{{ color_value | escape }}{% unless color_variant.available %} - {{ 'products.product.sold_out' | t }}{% endunless %}"
                    data-variant-id="{{ color_variant.id }}"
                    data-media-id="{{ media_for_color.id }}"
                    data-color-name="{{ color_value | escape }}"
                    data-src="{{ media_for_color | image_url: width: image_width }}"
                    data-srcset="{{ variant_srcset | strip }}"
                    data-sizes="{{ sizes_attr | strip }}"
                    {% unless color_variant.available %}data-sold-out="true"{% endunless %}
                  >
                    <span class="visually-hidden">{{ color_value | escape }}{% unless color_variant.available %} ({{ 'products.product.sold_out' | t }}){% endunless %}</span>
                  </button>
                {%- endif -%}
              {%- endif -%}
            {%- endunless -%}
          {%- endfor -%}

          {%- if color_values.size > max_swatches -%}
            <div class="swatch-more">+{{ color_values.size | minus: max_swatches }}</div>
          {%- endif -%}
        </div>

        {%- comment -%} Sold Out Indicator (shown when sold-out swatch is clicked) {%- endcomment -%}
        <div class="swatch-sold-out-indicator" hidden>
          <span class="sold-out-text">{{ 'products.product.sold_out' | t }}</span>
        </div>
      {%- endif -%}

      {%- comment -%} Price {%- endcomment -%}
      <div class="card__badge">
        <span class="badge badge--bottom-left color-{{ settings.sale_badge_color_scheme }}">
          {{ 'products.product.on_sale' | t }}
        </span>
      </div>

      <div class="price">
        {%- render 'price', product: product, price_class: 'price--on-sale' -%}
      </div>
    </div>

    {%- comment -%} Quick Add Button {%- endcomment -%}
    {%- if show_quick_add -%}
      <div class="quick-add no-js-hidden">
        {%- liquid
          assign is_trivial = false
          if product.has_only_default_variant or product.variants.size == 1
            assign is_trivial = true
          endif
        -%}
        <button
          class="button button--full-width button--secondary quick-add__button"
          data-quick-add
          data-product-url="{{ product.url | within: collection }}"
          data-product-id="{{ product.id }}"
          {% if is_trivial %}data-direct="true"{% endif %}
          aria-haspopup="dialog"
        >
          <span class="quick-add__text">
            {% if is_trivial %}{{ 'products.product.add_to_cart' | t }}{% else %}{{ 'products.product.choose_options' | t }}{% endif %}
          </span>
          <div class="loading-overlay hidden">
            <div class="loading-overlay__spinner">
              <svg
                aria-hidden="true"
                focusable="false"
                class="spinner"
                viewBox="0 0 66 66"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  class="path"
                  fill="none"
                  stroke-width="6"
                  cx="33"
                  cy="33"
                  r="30"
                ></circle>
              </svg>
            </div>
          </div>
        </button>
      </div>
    {%- endif -%}
  </div>
</div>

{% comment %}theme-check-disable RemoteAsset{% endcomment %}
<style>
  /* Responsive card media container */
  .card__media .media {
    position: relative;
    aspect-ratio: var(--card-aspect);
    overflow: hidden;
  }

  /* Base and preview images positioned identically */
  .card__media .media .card-media__primary,
  .card__media .media .card-media__preview {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    object-position: var(--card-object-position, center top);
  }

  /* Preview fades only */
  .card-media__preview {
    opacity: 0;
    pointer-events: none;
    transition: opacity 160ms ease;
  }

  /* Micro-optimizations for smooth fades */
  .card-media__primary,
  .card-media__preview {
    will-change: opacity;
    backface-visibility: hidden;
    contain: paint;
  }

  /* Card Swatches Styling */
  .card__swatches {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding: 0 1rem 1rem 0; /* Remove left padding to align first swatch to edge */
    justify-content: flex-start; /* Align to left */
  }

  .swatch-wrapper {
    position: relative;
  }

  .swatch {
    display: block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    background-color: #ccc; /* Fallback background */
  }

  .swatch:hover,
  .swatch:focus {
    transform: scale(1.1);
    box-shadow: 0 0 0 2px rgb(var(--color-background)), 0 0 0 4px rgb(var(--color-foreground));
  }

  .swatch-input:checked + .swatch {
    transform: scale(1.1);
    box-shadow: 0 0 0 2px rgb(var(--color-background)), 0 0 0 4px rgb(var(--color-foreground));
  }

  /* Sold-out swatch styling */
  .swatch--sold-out {
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;
  }

  .swatch--sold-out::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1px;
    height: 120%;
    background-color: rgb(var(--color-foreground));
    transform: translate(-50%, -50%) rotate(45deg);
    pointer-events: none;
  }

  .swatch--sold-out:hover,
  .swatch--sold-out:focus {
    transform: none;
    box-shadow: none;
  }

  /* Sold-out indicator styling */
  .swatch-sold-out-indicator {
    display: flex;
    justify-content: flex-end;
    margin-top: 0.5rem;
    padding-right: 1rem;
  }

  /* Ensure hidden attribute always hides the indicator */
  .swatch-sold-out-indicator[hidden] { display: none !important; }


  .sold-out-text {
    font-size: 0.75rem;
    color: rgb(var(--color-foreground));
    opacity: 0.7;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  /* Enhanced color mappings with !important for debugging */
  .swatch--green,
  .swatch--forest-green,
  .swatch--olive,
  .swatch--forest { background-color: #4a5d3a !important; }

  .swatch--pink,
  .swatch--light-pink,
  .swatch--rose,
  .swatch--dusty-pink { background-color: #f8bbd9 !important; }

  .swatch--black { background-color: #000000 !important; }
  .swatch--white { background-color: #ffffff !important; border: 2px solid #e0e0e0 !important; }
  .swatch--red { background-color: #dc2626 !important; }
  .swatch--blue { background-color: #2563eb !important; }
  .swatch--yellow { background-color: #facc15 !important; }
  .swatch--purple { background-color: #9333ea !important; }
  .swatch--orange { background-color: #ea580c !important; }
  .swatch--brown { background-color: #92400e !important; }
  .swatch--gray,
  .swatch--grey { background-color: #6b7280 !important; }
  .swatch--navy { background-color: #1e3a8a !important; }
  .swatch--beige { background-color: #f5f5dc !important; }
  .swatch--cream { background-color: #fffdd0 !important; }

  /* Fallback for colors not mapped above - always visible */
  .swatch:not([class*="swatch--"]) {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%) !important;
    background-size: 4px 4px;
    background-position: 0 0, 0 2px, 2px -2px, -2px 0px;
    border: 2px solid #ccc !important;
  }

  .visually-hidden {
    position: absolute !important;
    overflow: hidden;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    clip: rect(0 0 0 0);
    word-wrap: normal !important;
  }
</style>


