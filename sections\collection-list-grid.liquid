{% comment %}
  Collection List Grid Section
  - Grid display of multiple collections
  - Manual collection selection or automatic display
  - Configurable layout and card styling
  - Responsive design with hover effects
{% endcomment %}

{% liquid
  assign collections_to_show = section.settings.collections_to_show | default: 8
  assign columns_desktop = section.settings.columns_desktop | default: 4
  assign columns_mobile = section.settings.columns_mobile | default: 2
%}

<section
  id="CollectionListGrid-{{ section.id }}"
  class="collection-list-grid color-{{ section.settings.color_scheme | default: 'scheme-1' }} section-{{ section.id }}-padding"
  data-section="{{ section.id }}"
>
  <div class="collection-list-grid__inner page-width">
    
    {% comment %} Section Header {% endcomment %}
    {% if section.settings.heading != blank or section.settings.description != blank %}
      <div class="collection-list-grid__header">
        {% if section.settings.heading != blank %}
          <h2 class="collection-list-grid__title h2">{{ section.settings.heading }}</h2>
        {% endif %}
        
        {% if section.settings.description != blank %}
          <div class="collection-list-grid__description rte">{{ section.settings.description }}</div>
        {% endif %}
      </div>
    {% endif %}

    {% comment %} Collections Grid {% endcomment %}
    <div 
      class="collection-list-grid__grid"
      style="--columns-desktop: {{ columns_desktop }}; --columns-mobile: {{ columns_mobile }};"
    >
      {% comment %} Manual collection selection {% endcomment %}
      {% if section.blocks.size > 0 %}
        {% for block in section.blocks limit: collections_to_show %}
          {% liquid
            assign collection = block.settings.collection
            assign image = block.settings.image | default: collection.featured_image
            assign custom_title = block.settings.title
            assign custom_description = block.settings.description
          %}
          
          {% if collection != blank %}
            <div class="collection-list-grid__item" {{ block.shopify_attributes }}>
              <article class="collection-card">
                {% comment %} Collection Image {% endcomment %}
                <div class="collection-card__media">
                  {% if collection.url != blank %}
                    <a href="{{ collection.url }}" class="collection-card__link">
                  {% endif %}
                  
                  {% if image != blank %}
                    <div 
                      class="collection-card__image"
                      style="--aspect-ratio: {{ section.settings.image_ratio | default: '1 / 1' }};"
                    >
                      {{
                        image
                        | image_url: width: 600
                        | image_tag:
                          loading: 'lazy',
                          class: 'collection-card__img',
                          widths: '165, 360, 535, 750, 1070, 1500',
                          alt: image.alt | escape | default: collection.title
                      }}
                    </div>
                  {% else %}
                    <div class="collection-card__image collection-card__image--placeholder">
                      {{ 'collection-1' | placeholder_svg_tag: 'placeholder-svg' }}
                    </div>
                  {% endif %}
                  
                  {% comment %} Collection Info Overlay {% endcomment %}
                  <div class="collection-card__overlay">
                    <div class="collection-card__content">
                      <h3 class="collection-card__title">
                        {{ custom_title | default: collection.title | escape }}
                      </h3>
                      
                      {% if collection.products_count > 0 %}
                        <p class="collection-card__count">
                          {{ 'sections.collection_list.product_count' | t: count: collection.products_count }}
                        </p>
                      {% endif %}
                      
                      {% if custom_description != blank %}
                        <div class="collection-card__description">
                          {{ custom_description }}
                        </div>
                      {% elsif collection.description != blank and section.settings.show_description %}
                        <div class="collection-card__description">
                          {{ collection.description | strip_html | truncatewords: 20 }}
                        </div>
                      {% endif %}
                      
                      {% if section.settings.show_view_all %}
                        <span class="collection-card__button button button--secondary">
                          {{ 'sections.collection_list.view_all' | t }}
                        </span>
                      {% endif %}
                    </div>
                  </div>
                  
                  {% if collection.url != blank %}
                    </a>
                  {% endif %}
                </div>
              </article>
            </div>
          {% endif %}
        {% endfor %}
        
      {% comment %} Automatic collection display {% endcomment %}
      {% else %}
        {% assign collections_list = collections | where: 'published', true %}
        {% for collection in collections_list limit: collections_to_show %}
          {% unless collection.handle == 'frontpage' %}
            <div class="collection-list-grid__item">
              <article class="collection-card">
                {% comment %} Collection Image {% endcomment %}
                <div class="collection-card__media">
                  <a href="{{ collection.url }}" class="collection-card__link">
                    {% if collection.featured_image != blank %}
                      <div 
                        class="collection-card__image"
                        style="--aspect-ratio: {{ section.settings.image_ratio | default: '1 / 1' }};"
                      >
                        {{
                          collection.featured_image
                          | image_url: width: 600
                          | image_tag:
                            loading: 'lazy',
                            class: 'collection-card__img',
                            widths: '165, 360, 535, 750, 1070, 1500',
                            alt: collection.featured_image.alt | escape | default: collection.title
                        }}
                      </div>
                    {% else %}
                      <div class="collection-card__image collection-card__image--placeholder">
                        {{ 'collection-1' | placeholder_svg_tag: 'placeholder-svg' }}
                      </div>
                    {% endif %}
                    
                    {% comment %} Collection Info Overlay {% endcomment %}
                    <div class="collection-card__overlay">
                      <div class="collection-card__content">
                        <h3 class="collection-card__title">{{ collection.title | escape }}</h3>
                        
                        {% if collection.products_count > 0 %}
                          <p class="collection-card__count">
                            {{ 'sections.collection_list.product_count' | t: count: collection.products_count }}
                          </p>
                        {% endif %}
                        
                        {% if collection.description != blank and section.settings.show_description %}
                          <div class="collection-card__description">
                            {{ collection.description | strip_html | truncatewords: 20 }}
                          </div>
                        {% endif %}
                        
                        {% if section.settings.show_view_all %}
                          <span class="collection-card__button button button--secondary">
                            {{ 'sections.collection_list.view_all' | t }}
                          </span>
                        {% endif %}
                      </div>
                    </div>
                  </a>
                </div>
              </article>
            </div>
          {% endunless %}
        {% endfor %}
      {% endif %}
    </div>

    {% comment %} Empty State {% endcomment %}
    {% if section.blocks.size == 0 and collections_list.size == 0 %}
      <div class="collection-list-grid__empty">
        <h3>{{ 'sections.collection_list.empty' | t }}</h3>
        <p>{{ 'sections.collection_list.add_collections' | t }}</p>
      </div>
    {% endif %}
  </div>
</section>

{% stylesheet %}
  .collection-list-grid {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .collection-list-grid__inner {
    max-width: var(--page-width);
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .collection-list-grid__header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .collection-list-grid__title {
    margin-bottom: 1rem;
  }

  .collection-list-grid__description {
    max-width: 60rem;
    margin: 0 auto;
  }

  .collection-list-grid__grid {
    display: grid;
    grid-template-columns: repeat(var(--columns-desktop), 1fr);
    gap: 3rem 2rem;
  }

  .collection-list-grid__item {
    min-width: 0;
  }

  .collection-card {
    position: relative;
    height: 100%;
  }

  .collection-card__media {
    position: relative;
    height: 100%;
    border-radius: 1rem;
    overflow: hidden;
    background: rgba(var(--color-foreground), 0.04);
  }

  .collection-card__link {
    display: block;
    position: relative;
    height: 100%;
    text-decoration: none;
    color: inherit;
  }

  .collection-card__image {
    width: 100%;
    height: 100%;
    min-height: 25rem;
    aspect-ratio: var(--aspect-ratio);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }

  .collection-card__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .collection-card:hover .collection-card__img {
    transform: scale(1.05);
  }

  .collection-card__image--placeholder {
    background: rgba(var(--color-foreground), 0.04);
  }

  .collection-card__image--placeholder svg {
    width: 40%;
    height: 40%;
    opacity: 0.3;
  }

  .collection-card__overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(
      to bottom,
      transparent 0%,
      transparent 40%,
      rgba(0, 0, 0, 0.4) 70%,
      rgba(0, 0, 0, 0.7) 100%
    );
    display: flex;
    align-items: flex-end;
    padding: 2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .collection-card:hover .collection-card__overlay {
    opacity: 1;
  }

  .collection-card__content {
    color: white;
    text-align: left;
    width: 100%;
  }

  .collection-card__title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.2;
  }

  .collection-card__count {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
  }

  .collection-card__description {
    font-size: 1.4rem;
    line-height: 1.4;
    margin-bottom: 1.5rem;
    opacity: 0.9;
  }

  .collection-card__button {
    font-size: 1.3rem;
    padding: 0.8rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: 0.1rem solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
  }

  .collection-card__button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-0.2rem);
  }

  .collection-list-grid__empty {
    text-align: center;
    padding: 6rem 0;
    grid-column: 1 / -1;
  }

  .collection-list-grid__empty h3 {
    margin-bottom: 1rem;
    font-size: 2.4rem;
  }

  .collection-list-grid__empty p {
    color: rgba(var(--color-foreground), 0.7);
    font-size: 1.6rem;
  }

  @media screen and (max-width: 989px) {
    .collection-list-grid__grid {
      grid-template-columns: repeat(var(--columns-mobile), 1fr);
      gap: 2rem 1.5rem;
    }

    .collection-card__overlay {
      opacity: 1;
      background: linear-gradient(
        to bottom,
        transparent 0%,
        transparent 30%,
        rgba(0, 0, 0, 0.6) 60%,
        rgba(0, 0, 0, 0.8) 100%
      );
    }

    .collection-card__title {
      font-size: 1.8rem;
    }

    .collection-card__content {
      padding: 1.5rem;
    }
  }

  @media screen and (max-width: 749px) {
    .collection-list-grid {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }

    .collection-list-grid__header {
      margin-bottom: 3rem;
    }

    .collection-card__image {
      min-height: 20rem;
    }

    .collection-card__overlay {
      padding: 1.5rem;
    }

    .collection-card__title {
      font-size: 1.6rem;
    }

    .collection-card__description {
      font-size: 1.3rem;
      margin-bottom: 1rem;
    }
  }

  @media screen and (max-width: 479px) {
    .collection-list-grid__grid {
      grid-template-columns: 1fr;
    }

    .collection-card__image {
      min-height: 25rem;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Collection list grid",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Section"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Collections"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "range",
      "id": "collections_to_show",
      "min": 2,
      "max": 12,
      "step": 1,
      "default": 8,
      "label": "Collections to show"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "Number of columns on desktop"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Number of columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "Image ratio",
      "options": [
        {
          "value": "2 / 3",
          "label": "Portrait"
        },
        {
          "value": "1 / 1",
          "label": "Square"
        },
        {
          "value": "3 / 2",
          "label": "Landscape"
        }
      ],
      "default": "1 / 1"
    },
    {
      "type": "header",
      "content": "Collection cards"
    },
    {
      "type": "checkbox",
      "id": "show_description",
      "default": false,
      "label": "Show collection description"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "Show 'View all' button"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Custom image",
          "info": "Leave blank to use collection featured image"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Custom title",
          "info": "Leave blank to use collection title"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Custom description",
          "info": "Leave blank to use collection description"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Collection list grid",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
