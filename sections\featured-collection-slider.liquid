{%- comment -%}
  Featured Collection Slider Section
  
  Purpose: Horizontal scrolling product carousel
  - Displays products from a selected collection
  - Configurable visible items and rows
  - Optional autoplay functionality
  - Accessible slider with keyboard navigation
{%- endcomment -%}

<script src="{{ 'section-featured-collection-slider.js' | asset_url }}" defer></script>

{% liquid
  assign collection = section.settings.collection
  assign visible_items = section.settings.visible_items | default: 4
  assign rows = section.settings.rows | default: 1
  assign autoplay = section.settings.autoplay | default: false
  assign color_scheme = section.settings.color_scheme | default: 'scheme-1'
  assign section_id = 'FeaturedCollectionSlider-' | append: section.id
%}

<section
  id="{{ section_id }}"
  class="featured-collection-slider color-{{ color_scheme }} section-{{ section.id }}-padding"
  data-section="{{ section.id }}"
  data-featured-collection-slider
  {% if autoplay %}data-autoplay="true"{% endif %}
>
  <div class="featured-collection-slider__inner page-width">
    
    {% comment %} Section Header {% endcomment %}
    <div class="featured-collection-slider__header">
      {% if section.settings.heading != blank %}
        <h2 class="featured-collection-slider__title h2">{{ section.settings.heading }}</h2>
      {% elsif collection != blank %}
        <h2 class="featured-collection-slider__title h2">{{ collection.title }}</h2>
      {% endif %}
      
      {% if section.settings.description != blank %}
        <div class="featured-collection-slider__description rte">{{ section.settings.description }}</div>
      {% elsif collection != blank and collection.description != blank %}
        <div class="featured-collection-slider__description rte">{{ collection.description }}</div>
      {% endif %}
      
      {% if section.settings.button_label != blank %}
        {% assign button_link = section.settings.button_link | default: collection.url %}
        <a href="{{ button_link }}" class="featured-collection-slider__view-all button button--secondary">
          {{ section.settings.button_label }}
        </a>
      {% endif %}
    </div>

    {% comment %} Product Slider {% endcomment %}
    {% if collection != blank and collection.products.size > 0 %}
      <div 
        class="featured-collection-slider__slider{% if rows == 2 %} featured-collection-slider__slider--two-rows{% endif %}"
        role="region"
        aria-roledescription="carousel"
        aria-label="{{ collection.title | escape }} products"
        data-slider
        data-visible-items="{{ visible_items }}"
        data-total-products="{{ collection.products.size }}"
      >
        <div class="featured-collection-slider__viewport">
          <div 
            class="featured-collection-slider__track"
            style="--visible-items: {{ visible_items }}; --rows: {{ rows }};"
            tabindex="0"
            role="group"
            aria-live="polite"
            aria-atomic="false"
            data-slider-track
          >
            {% for product in collection.products %}
              <div 
                class="featured-collection-slider__slide"
                role="group"
                aria-roledescription="slide"
                aria-label="Product {{ forloop.index }} of {{ collection.products.size }}: {{ product.title | escape }}"
                data-slide-index="{{ forloop.index0 }}"
              >
                {% render 'card-product',
                  product: product,
                  image_ratio: section.settings.image_ratio,
                  show_vendor: section.settings.show_vendor,
                  show_secondary_image: section.settings.show_secondary_image,
                  show_quick_add: section.settings.show_quick_add,
                  section_id: section.id
                %}
              </div>
            {% endfor %}
          </div>
        </div>

        {% comment %} Slider Controls {% endcomment %}
        {% assign slides_count = collection.products.size %}
        {% assign max_visible = visible_items | times: rows %}
        {% if slides_count > max_visible %}
          <div class="featured-collection-slider__controls">
            <button 
              class="featured-collection-slider__button featured-collection-slider__button--prev"
              aria-controls="{{ section_id }}-slider-track"
              aria-label="Previous products"
              data-slider-prev
              disabled
            >
              <span class="visually-hidden">Previous products</span>
              <svg aria-hidden="true" focusable="false" width="16" height="16" viewBox="0 0 16 16">
                <path d="M10 12L6 8l4-4" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            
            <div class="featured-collection-slider__pagination" aria-live="polite" aria-atomic="true">
              <span data-slide-counter>1</span>-<span data-visible-counter>{{ max_visible }}</span> of {{ slides_count }}
            </div>
            
            <button 
              class="featured-collection-slider__button featured-collection-slider__button--next"
              aria-controls="{{ section_id }}-slider-track"
              aria-label="Next products"
              data-slider-next
            >
              <span class="visually-hidden">Next products</span>
              <svg aria-hidden="true" focusable="false" width="16" height="16" viewBox="0 0 16 16">
                <path d="M6 4l4 4-4 4" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>

          {% comment %} Autoplay Controls {% endcomment %}
          {% if autoplay %}
            <div class="featured-collection-slider__autoplay-controls">
              <button 
                class="featured-collection-slider__autoplay-button"
                aria-label="Pause slideshow"
                data-autoplay-toggle
              >
                <span class="visually-hidden">Pause slideshow</span>
                <svg class="featured-collection-slider__autoplay-icon--pause" aria-hidden="true" focusable="false" width="16" height="16" viewBox="0 0 16 16">
                  <rect x="6" y="4" width="1.5" height="8" fill="currentColor"/>
                  <rect x="8.5" y="4" width="1.5" height="8" fill="currentColor"/>
                </svg>
                <svg class="featured-collection-slider__autoplay-icon--play" aria-hidden="true" focusable="false" width="16" height="16" viewBox="0 0 16 16" style="display: none;">
                  <path d="M6 4l6 4-6 4V4z" fill="currentColor"/>
                </svg>
              </button>
            </div>
          {% endif %}
        {% endif %}
      </div>

    {% elsif collection == blank %}
      {% comment %} Empty State {% endcomment %}
      <div class="featured-collection-slider__empty">
        <h3>{{ 'sections.featured_collection.no_collection' | t }}</h3>
        <p>{{ 'sections.featured_collection.select_collection' | t }}</p>
      </div>
    
    {% else %}
      {% comment %} No Products State {% endcomment %}
      <div class="featured-collection-slider__empty">
        <h3>{{ 'sections.featured_collection.no_products' | t }}</h3>
        <p>{{ 'sections.featured_collection.add_products' | t }}</p>
      </div>
    {% endif %}
  </div>
</section>

{% stylesheet %}
  .featured-collection-slider {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .featured-collection-slider__inner {
    max-width: var(--page-width);
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .featured-collection-slider__header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 4rem;
    gap: 2rem;
  }

  .featured-collection-slider__title {
    font-size: clamp(2.4rem, 4vw, 4.8rem);
    margin: 0;
  }

  .featured-collection-slider__description {
    max-width: 60rem;
    font-size: 1.6rem;
    line-height: 1.6;
    color: rgba(var(--color-foreground), 0.8);
  }

  .featured-collection-slider__view-all {
    margin-top: 1rem;
  }

  .featured-collection-slider__slider {
    position: relative;
  }

  .featured-collection-slider__viewport {
    overflow: hidden;
    border-radius: 0.8rem;
  }

  .featured-collection-slider__track {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    grid-template-rows: repeat(var(--rows), 1fr);
    grid-auto-flow: column;
    gap: 2rem;
    transition: transform 0.3s ease;
    outline: none;
  }

  .featured-collection-slider__track:focus {
    outline: 0.2rem solid rgb(var(--color-accent-1));
    outline-offset: 0.2rem;
  }

  .featured-collection-slider__slider--two-rows .featured-collection-slider__track {
    grid-template-columns: repeat(auto-fit, minmax(280px, calc((100% - 2rem) / var(--visible-items))));
  }

  .featured-collection-slider__slide {
    min-width: 0;
  }

  .featured-collection-slider__controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
  }

  .featured-collection-slider__button {
    width: 4.8rem;
    height: 4.8rem;
    border: 0.1rem solid rgba(var(--color-foreground), 0.2);
    border-radius: 50%;
    background: rgb(var(--color-background));
    color: rgb(var(--color-foreground));
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .featured-collection-slider__button:hover:not(:disabled) {
    border-color: rgb(var(--color-foreground));
    background: rgb(var(--color-foreground));
    color: rgb(var(--color-background));
  }

  .featured-collection-slider__button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .featured-collection-slider__pagination {
    font-size: 1.4rem;
    color: rgba(var(--color-foreground), 0.7);
    min-width: 8rem;
    text-align: center;
  }

  .featured-collection-slider__autoplay-controls {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
  }

  .featured-collection-slider__autoplay-button {
    width: 3.6rem;
    height: 3.6rem;
    border: 0.1rem solid rgba(var(--color-foreground), 0.2);
    border-radius: 50%;
    background: rgb(var(--color-background));
    color: rgba(var(--color-foreground), 0.7);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .featured-collection-slider__autoplay-button:hover {
    border-color: rgb(var(--color-foreground));
    color: rgb(var(--color-foreground));
  }

  .featured-collection-slider__empty {
    text-align: center;
    padding: 6rem 2rem;
    color: rgba(var(--color-foreground), 0.6);
  }

  .featured-collection-slider__empty h3 {
    font-size: 2.4rem;
    margin-bottom: 1rem;
  }

  .featured-collection-slider__empty p {
    font-size: 1.6rem;
    line-height: 1.6;
  }

  @media screen and (min-width: 750px) {
    .featured-collection-slider__header {
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-end;
      text-align: left;
    }

    .featured-collection-slider__view-all {
      margin-top: 0;
    }

    .featured-collection-slider__track {
      grid-template-columns: repeat(var(--visible-items), 1fr);
    }
  }

  @media screen and (max-width: 749px) {
    .featured-collection-slider {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }

    .featured-collection-slider__header {
      margin-bottom: 3rem;
      gap: 1.5rem;
    }

    .featured-collection-slider__track {
      grid-template-columns: repeat(2, 1fr);
    }

    .featured-collection-slider__controls {
      gap: 1rem;
      margin-top: 2rem;
    }

    .featured-collection-slider__button {
      width: 4rem;
      height: 4rem;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Featured collection",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Collection"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Custom heading",
      "info": "Leave blank to use collection title"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Custom description",
      "info": "Leave blank to use collection description"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label",
      "default": "View collection"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link",
      "info": "Leave blank to use collection URL"
    },
    {
      "type": "header",
      "content": "Slider settings"
    },
    {
      "type": "range",
      "id": "visible_items",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "Products visible at once"
    },
    {
      "type": "select",
      "id": "rows",
      "label": "Rows",
      "options": [
        {
          "value": "1",
          "label": "1 row"
        },
        {
          "value": "2",
          "label": "2 rows"
        }
      ],
      "default": "1"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "default": false,
      "label": "Enable autoplay",
      "info": "Automatically advance slides every 5 seconds"
    },
    {
      "type": "header",
      "content": "Product cards"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "Image ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to image"
        },
        {
          "value": "square",
          "label": "Square (1:1)"
        },
        {
          "value": "portrait",
          "label": "Portrait (3:4)"
        }
      ],
      "default": "adapt"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "Show second product image on hover"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "Show product vendor"
    },
    {
      "type": "checkbox",
      "id": "show_quick_add",
      "default": false,
      "label": "Show quick add"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    }
  ],
  "presets": [
    {
      "name": "Featured collection"
    }
  ]
}
{% endschema %}
