[{"name": "theme_info", "theme_name": "Skeleton", "theme_version": "0.1.0", "theme_author": "Shopify", "theme_documentation_url": "https://help.shopify.com/manual/online-store/themes", "theme_support_url": "https://support.shopify.com/"}, {"name": "t:general.typography", "settings": [{"type": "header", "content": "t:general.fonts"}, {"type": "font_picker", "id": "type_primary_font", "default": "jost_n4", "label": "t:general.primary"}]}, {"name": "t:general.layout", "settings": [{"type": "select", "id": "max_page_width", "label": "t:labels.page_width", "options": [{"value": "90rem", "label": "t:options.page_width.narrow"}, {"value": "110rem", "label": "t:options.page_width.wide"}], "default": "90rem"}, {"type": "range", "id": "min_page_margin", "min": 10, "max": 100, "step": 1, "unit": "px", "label": "t:labels.page_margin", "default": 20}]}, {"name": "t:general.color_schemes", "settings": [{"type": "color_scheme_group", "id": "color_schemes", "role": {"background": "background", "text": "text", "primary_button": "solid_button_background", "on_primary_button": "solid_button_label", "secondary_button": "background", "on_secondary_button": "outline_button_label", "primary_button_border": "solid_button_background", "secondary_button_border": "outline_button_label", "links": "accent_1", "icons": "text"}, "definition": [{"type": "color", "id": "background", "label": "t:settings_schema.colors.settings.background.label", "default": "#FFFFFF"}, {"type": "color", "id": "background_gradient", "label": "t:settings_schema.colors.settings.background_gradient.label"}, {"type": "color", "id": "text", "label": "t:settings_schema.colors.settings.text.label", "default": "#121212"}, {"type": "color", "id": "accent_1", "label": "t:settings_schema.colors.settings.accent_1.label", "default": "#121212"}, {"type": "color", "id": "accent_2", "label": "t:settings_schema.colors.settings.accent_2.label", "default": "#334FB4"}, {"type": "color", "id": "outline_button_label", "label": "t:settings_schema.colors.settings.outline_button_label.label", "default": "#121212"}, {"type": "color", "id": "solid_button_background", "label": "t:settings_schema.colors.settings.solid_button_background.label", "default": "#121212"}, {"type": "color", "id": "solid_button_label", "label": "t:settings_schema.colors.settings.solid_button_label.label", "default": "#FFFFFF"}]}]}, {"name": "t:general.colors", "settings": [{"type": "range", "id": "input_corner_radius", "min": 0, "max": 10, "step": 1, "unit": "px", "label": "t:labels.input_corner_radius", "default": 4}]}]