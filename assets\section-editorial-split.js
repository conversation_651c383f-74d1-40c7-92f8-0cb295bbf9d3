/**
 * Editorial Split Slider
 * Lightweight, accessible carousel for editorial sections
 */

class EditorialSplitSlider {
  constructor(element) {
    this.slider = element;
    this.track = element.querySelector('[data-slider-track]');
    this.slides = element.querySelectorAll('[data-slide-index]');
    this.prevButton = element.querySelector('[data-slider-prev]');
    this.nextButton = element.querySelector('[data-slider-next]');
    this.counter = element.querySelector('[data-slide-counter]');

    if (!this.track || !this.slides.length) return;

    this.currentIndex = 0;
    this.slideCount = this.slides.length;

    this.init();
  }

  init() {
    this.bindEvents();
    this.updateSlider();
    this.announceSlide();
  }

  bindEvents() {
    // Button controls
    if (this.prevButton) {
      this.prevButton.addEventListener('click', () => this.goToPrev());
    }
    if (this.nextButton) {
      this.nextButton.addEventListener('click', () => this.goToNext());
    }

    // Keyboard navigation
    this.track.addEventListener('keydown', (e) => {
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          this.goToPrev();
          break;
        case 'ArrowRight':
          e.preventDefault();
          this.goToNext();
          break;
        case 'Home':
          e.preventDefault();
          this.goToSlide(0);
          break;
        case 'End':
          e.preventDefault();
          this.goToSlide(this.slideCount - 1);
          break;
      }
    });

    // Touch/swipe support (basic)
    let startX = 0;
    let isDragging = false;

    this.track.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      isDragging = true;
    }, { passive: true });

    this.track.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      e.preventDefault();
    });

    this.track.addEventListener('touchend', (e) => {
      if (!isDragging) return;
      isDragging = false;

      const endX = e.changedTouches[0].clientX;
      const diff = startX - endX;
      const threshold = 50;

      if (Math.abs(diff) > threshold) {
        if (diff > 0) {
          this.goToNext();
        } else {
          this.goToPrev();
        }
      }
    }, { passive: true });
  }

  goToPrev() {
    if (this.currentIndex > 0) {
      this.goToSlide(this.currentIndex - 1);
    }
  }

  goToNext() {
    if (this.currentIndex < this.slideCount - 1) {
      this.goToSlide(this.currentIndex + 1);
    }
  }

  goToSlide(index) {
    if (index >= 0 && index < this.slideCount) {
      this.currentIndex = index;
      this.updateSlider();
      this.announceSlide();
    }
  }

  updateSlider() {
    // Update transform
    const translateX = -100 * this.currentIndex;
    this.track.style.transform = `translateX(${translateX}%)`;

    // Update button states
    if (this.prevButton) {
      this.prevButton.disabled = this.currentIndex === 0;
      this.prevButton.setAttribute('aria-disabled', this.currentIndex === 0);
    }
    if (this.nextButton) {
      this.nextButton.disabled = this.currentIndex === this.slideCount - 1;
      this.nextButton.setAttribute('aria-disabled', this.currentIndex === this.slideCount - 1);
    }

    // Update counter
    if (this.counter) {
      this.counter.textContent = this.currentIndex + 1;
    }

    // Update slide visibility for screen readers
    this.slides.forEach((slide, index) => {
      const isVisible = index === this.currentIndex;
      slide.setAttribute('aria-hidden', !isVisible);
      slide.style.visibility = isVisible ? 'visible' : 'hidden';
    });
  }

  announceSlide() {
    // Announce slide change for screen readers
    const announcement = `Slide ${this.currentIndex + 1} of ${this.slideCount}`;
    
    // Use existing aria-live region or create one
    let liveRegion = this.slider.querySelector('[aria-live="polite"]');
    if (!liveRegion) {
      liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'visually-hidden';
      this.slider.appendChild(liveRegion);
    }
    
    // Brief delay to ensure screen readers pick up the change
    setTimeout(() => {
      liveRegion.textContent = announcement;
    }, 100);
  }
}

// Initialize sliders
function initEditorialSplitSliders() {
  const sliders = document.querySelectorAll('[data-editorial-split] [data-slider]');
  
  sliders.forEach(slider => {
    // Prevent duplicate initialization
    if (slider.dataset.sliderInitialized) return;
    
    new EditorialSplitSlider(slider);
    slider.dataset.sliderInitialized = 'true';
  });
}

// Initialize on DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initEditorialSplitSliders);
} else {
  initEditorialSplitSliders();
}

// Shopify theme editor support
document.addEventListener('shopify:section:load', (e) => {
  if (e.target.querySelector('[data-editorial-split]')) {
    setTimeout(initEditorialSplitSliders, 100);
  }
});

document.addEventListener('shopify:section:unload', (e) => {
  const sliders = e.target.querySelectorAll('[data-slider]');
  sliders.forEach(slider => {
    delete slider.dataset.sliderInitialized;
  });
});

// Handle section reordering and selection
document.addEventListener('shopify:section:reorder', initEditorialSplitSliders);
document.addEventListener('shopify:section:select', (e) => {
  if (e.target.querySelector('[data-editorial-split]')) {
    setTimeout(initEditorialSplitSliders, 100);
  }
});