<!doctype html>
<html lang="{{ request.locale.iso_code }}">
  <head>
    {%- comment -%} CDN Warming - Preconnect to Shopify CDN {%- endcomment -%}
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
    {% comment %}theme-check-disable RemoteAsset{% endcomment %}
    <link rel="dns-prefetch" href="//cdn.shopify.com">
    
    {%- comment -%} LCP Image Preload for Product Pages {%- endcomment -%}
    {%- if template.name == 'product' and product.featured_media -%}
      {%- assign featured = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}
      {% comment %}theme-check-disable AssetPreload{% endcomment %}
      <link
        rel="preload"
        as="image"
        href="{{ featured | image_url: width: 1800 }}"
        imagesrcset="{{ featured | image_url: width: 1100 }} 1100w, {{ featured | image_url: width: 1500 }} 1500w, {{ featured | image_url: width: 1800 }} 1800w"
        imagesizes="(min-width: 1200px) 50vw, 100vw"
        fetchpriority="high">
    {%- endif -%}

    {% # Inlined CSS Variables %}
    {% render 'css-variables' %}

    {% # Critical CSS with proper preload %}
    {{ 'critical.css' | asset_url | stylesheet_tag: preload: true }}

    {% # Social, title, etc. %}
    {% render 'meta-tags' %}

    {{ content_for_header }}
  </head>

  <body class="color-background-1">
    {% sections 'header-group' %}

    <main id="MainContent" class="main-content color-background-1" role="main" tabindex="-1">
      {{ content_for_layout }}
    </main>

    {% sections 'footer-group' %}
    {% section 'newsletter-popup' %}

    {%- comment -%} Quick Add Modal {%- endcomment -%}
    {% render 'quick-add-modal' %}
    {{ 'quick-add.css' | asset_url | stylesheet_tag }}
    <script src="{{ 'quick-add.js' | asset_url }}" defer></script>
  </body>
</html>
