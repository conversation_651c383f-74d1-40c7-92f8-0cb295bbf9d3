{%- comment -%}
  Price Display Snippet
  Renders product pricing with sale indicators
  
  Parameters:
  - product: product object
  - price_class: additional CSS class
{%- endcomment -%}

{%- liquid
  assign compare_at_price = product.compare_at_price
  assign price = product.price | default: 1999
  assign price_class = price_class | default: ''
  assign money_price = price | money
  if settings.currency_code_enabled
    assign money_price = price | money_with_currency
  endif

  if product.price_varies
    assign money_price = 'products.product.price.from_price_html' | t: price: money_price
  endif
-%}

<div class="price {{ price_class }}">
  <div class="price__container">
    {%- comment -%}
      Explanation of description list:
        - div.price__regular: Displayed when there are no variants on sale
        - div.price__sale: Displayed when a variant is a sale
    {%- endcomment -%}
    <div class="price__regular">
      <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.regular_price' | t }}</span>
      <span class="price-item price-item--regular">
        {{ money_price }}
      </span>
    </div>
    <div class="price__sale">
      {%- unless product.price_varies == false and product.compare_at_price_varies %}
        <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.regular_price' | t }}</span>
        <span>
          <s class="price-item price-item--regular">
            {% if settings.currency_code_enabled %}
              {{ compare_at_price | money_with_currency }}
            {% else %}
              {{ compare_at_price | money }}
            {% endif %}
          </s>
        </span>
      {%- endunless -%}
      <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.sale_price' | t }}</span>
      <span class="price-item price-item--sale price-item--last">
        {{ money_price }}
      </span>
    </div>
    <small class="unit-price caption{% unless product.selected_or_first_available_variant.unit_price_measurement %} hidden{% endunless %}">
      <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
      <span class="price-item price-item--last">
        <span>{{- product.selected_or_first_available_variant.unit_price | money -}}</span>
        <span aria-hidden="true">/</span>
        <span class="visually-hidden">&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span>
        <span>
          {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
            {{- product.selected_or_first_available_variant.unit_price_measurement.reference_value -}}
          {%- endif -%}
          {{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}
        </span>
      </span>
    </small>
  </div>
</div>

<style>
  .price {
    color: inherit;
    font-size: 1.25rem;
    line-height: 1.2;
  }
  
  .price__container {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.5rem;
  }
  
  .price__regular:empty,
  .price__sale:empty,
  .price__sale--no-compare:empty {
    display: none;
  }
  
  .price__sale {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.5rem;
  }
  
  .price-item--regular {
    color: rgba(var(--color-foreground), 0.75);
    font-size: 0.875em;
  }
  
  .price-item--sale {
    color: rgb(var(--color-base-accent-1));
    font-weight: 600;
  }
  
  .unit-price {
    color: rgba(var(--color-foreground), 0.7);
    font-size: 0.75em;
    margin-top: 0.25rem;
  }
  
  .visually-hidden {
    position: absolute !important;
    overflow: hidden;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    clip: rect(0 0 0 0);
    word-wrap: normal !important;
  }
  
  .visually-hidden--inline {
    margin: 0;
    height: 1em;
  }
</style>
